import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 导入Vant组件库
import { 
  ConfigProvider, 
  NavBar, 
  List, 
  Cell, 
  Tag, 
  Button,
  Icon,
  Toast,
  Dialog,
  Form,
  Field,
  Popup,
  ActionSheet,
  SwipeCell,
  Loading,
  PullRefresh,
  Empty
} from 'vant'

// 导入Vant样式
import 'vant/lib/index.css'

const app = createApp(App)

// 注册Vant组件
app.use(ConfigProvider)
app.use(NavBar)
app.use(List)
app.use(Cell)
app.use(Tag)
app.use(Button)
app.use(Icon)
app.use(Toast)
app.use(Dialog)
app.use(Form)
app.use(Field)
app.use(Popup)
app.use(ActionSheet)
app.use(SwipeCell)
app.use(Loading)
app.use(PullRefresh)
app.use(Empty)

app.use(store).use(router).mount('#app') 