import { createStore } from 'vuex'
import api from '../services/api'

export default createStore({
  state: {
    websites: [],
    stats: {
      total: 0,
      running: 0,
      down: 0,
      internal: 0,
      external: 0,
      uptime: 100
    },
    loading: false
  },
  mutations: {
    SET_WEBSITES(state, websites) {
      state.websites = websites
    },
    SET_STATS(state, stats) {
      state.stats = stats
    },
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    ADD_WEBSITE(state, website) {
      state.websites.unshift(website)
    },
    UPDATE_WEBSITE(state, updatedWebsite) {
      const index = state.websites.findIndex(w => w._id === updatedWebsite._id)
      if (index !== -1) {
        state.websites.splice(index, 1, updatedWebsite)
      }
    },
    DELETE_WEBSITE(state, websiteId) {
      state.websites = state.websites.filter(w => w._id !== websiteId)
    }
  },
  actions: {
    async fetchWebsites({ commit }) {
      try {
        commit('SET_LOADING', true)
        const response = await api.getWebsites()
        commit('SET_WEBSITES', response.data.data)
      } catch (error) {
        console.error('获取网站列表失败:', error)
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    async fetchStats({ commit }) {
      try {
        const response = await api.getStats()
        commit('SET_STATS', response.data.data)
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    },
    
    async addWebsite({ commit }, websiteData) {
      try {
        const response = await api.addWebsite(websiteData)
        commit('ADD_WEBSITE', response.data.data)
        return response.data
      } catch (error) {
        console.error('添加网站失败:', error)
        throw error
      }
    },
    
    async updateWebsite({ commit }, { id, data }) {
      try {
        const response = await api.updateWebsite(id, data)
        commit('UPDATE_WEBSITE', response.data.data)
        return response.data
      } catch (error) {
        console.error('更新网站失败:', error)
        throw error
      }
    },
    
    async deleteWebsite({ commit }, id) {
      try {
        await api.deleteWebsite(id)
        commit('DELETE_WEBSITE', id)
      } catch (error) {
        console.error('删除网站失败:', error)
        throw error
      }
    },
    
    async checkWebsite({ commit }, id) {
      try {
        const response = await api.checkWebsite(id)
        // 检查完成后刷新网站列表
        const websitesResponse = await api.getWebsites()
        commit('SET_WEBSITES', websitesResponse.data.data)
        return response.data
      } catch (error) {
        console.error('检查网站失败:', error)
        throw error
      }
    }
  }
}) 