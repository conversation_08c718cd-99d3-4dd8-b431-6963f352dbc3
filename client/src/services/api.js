import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API请求:', config.method.toUpperCase(), config.url)
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('API请求失败:', error.message)
    if (error.response) {
      console.error('错误状态码:', error.response.status)
      console.error('错误数据:', error.response.data)
    }
    return Promise.reject(error)
  }
)

export default {
  // 获取所有网站
  getWebsites() {
    return api.get('/monitor/websites')
  },
  
  // 添加网站
  addWebsite(websiteData) {
    return api.post('/monitor/websites', websiteData)
  },
  
  // 更新网站
  updateWebsite(id, websiteData) {
    return api.put(`/monitor/websites/${id}`, websiteData)
  },
  
  // 删除网站
  deleteWebsite(id) {
    return api.delete(`/monitor/websites/${id}`)
  },
  
  // 检查网站
  checkWebsite(id) {
    return api.post(`/monitor/check/${id}`)
  },
  
  // 获取统计信息
  getStats() {
    return api.get('/monitor/stats')
  }
} 