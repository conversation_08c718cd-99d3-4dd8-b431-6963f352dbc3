import { createRouter, createWebHistory } from 'vue-router'
import Monitor from '../views/Monitor.vue'
import AddWebsite from '../views/AddWebsite.vue'
import WebsiteDetail from '../views/WebsiteDetail.vue'

const routes = [
  {
    path: '/',
    name: 'Monitor',
    component: Monitor
  },
  {
    path: '/add',
    name: 'AddWebsite',
    component: AddWebsite
  },
  {
    path: '/detail/:id',
    name: 'WebsiteDetail',
    component: WebsiteDetail
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router 