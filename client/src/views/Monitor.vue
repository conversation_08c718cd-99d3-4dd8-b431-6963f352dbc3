<template>
  <div class="monitor-page">
    <!-- 导航栏 -->
    <van-nav-bar title="监控管理" fixed>
      <!-- <template #right>
        <van-button 
          type="primary" 
          size="small" 
          @click="$router.push('/add')"
        >
          添加
        </van-button>
      </template> -->
    </van-nav-bar>

    <!-- 下拉刷新 -->
    <van-pull-refresh 
      v-model="refreshing" 
      @refresh="onRefresh"
      class="content-container"
    >
      <!-- 统计信息卡片 -->
      <div class="stats-card">
        <div class="stats-item">
          <span class="stats-value">{{ stats.total }}</span>
          <span class="stats-label">总数</span>
        </div>
        <div class="stats-item">
          <span class="stats-value running">{{ stats.running }}</span>
          <span class="stats-label">运行中</span>
        </div>
        <div class="stats-item">
          <span class="stats-value down">{{ stats.down }}</span>
          <span class="stats-label">中断</span>
        </div>
        <div class="stats-item">
          <span class="stats-value">{{ stats.uptime }}%</span>
          <span class="stats-label">可用率</span>
        </div>
      </div>

      <!-- 网站列表 -->
      <van-list
        v-model:loading="loading"
        finished
        @load="onLoad"
      >
        <div v-if="websites.length === 0 && !loading" class="empty-container">
          <van-empty description="暂无监控网站" />
        </div>
        
        <van-swipe-cell 
          v-for="website in websites" 
          :key="website._id"
        >
          <van-cell 
            :title="website.name"
            :label="`${website.operatingHours.start} ~ ${website.operatingHours.end}`"
            @click="goToDetail(website._id)"
            class="website-cell"
          >
            <template #right-icon>
              <div class="website-status">
                <van-tag 
                  :type="getNetworkTagType(website.networkType)"
                  size="small"
                  class="network-tag"
                >
                {{ formatLastChecked(website.lastChecked)}}
                </van-tag>
                <van-tag 
                  :type="getStatusTagType(website.status)"
                  size="small"
                  class="status-tag"
                >
                  {{ website.status }}
                </van-tag>
              </div>
            </template>
          </van-cell>
          
          <!-- 滑动操作 -->
          <template #right>
            <van-button 
              square 
              type="primary" 
              text="检查"
              @click="checkWebsite(website._id)"
              class="swipe-button"
            />
            <van-button 
              square 
              type="danger" 
              text="删除"
              @click="deleteWebsite(website._id)"
              class="swipe-button"
            />
          </template>
        </van-swipe-cell>
      </van-list>
    </van-pull-refresh>

    <!-- 加载状态 -->
    <van-loading 
      v-if="loading && websites.length === 0"
      class="loading-center"
      size="24px"
    >
      加载中...
    </van-loading>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { Toast, Dialog } from 'vant'

export default {
  name: 'Monitor',
  data() {
    return {
      refreshing: false
    }
  },
  computed: {
    ...mapState(['websites', 'stats', 'loading'])
  },
  async created() {
    await this.loadData()
    // 每30秒自动刷新一次
    this.autoRefreshTimer = setInterval(() => {
      this.fetchWebsites()
      this.fetchStats()
    }, 30000)
  },
  beforeUnmount() {
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer)
    }
  },
  methods: {
    ...mapActions(['fetchWebsites', 'fetchStats', 'deleteWebsite', 'checkWebsite']),
    
    async loadData() {
      try {
        await Promise.all([
          this.fetchWebsites(),
          this.fetchStats()
        ])
      } catch (error) {
        Toast.fail('加载数据失败')
      }
    },
    
    async onRefresh() {
      try {
        await this.loadData()
        Toast.success('刷新成功')
      } catch (error) {
        Toast.fail('刷新失败')
      } finally {
        this.refreshing = false
      }
    },
    
    onLoad() {
      // 这里可以实现分页加载
    },
    
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '系统中断': 'danger',
        '被修改': 'warning',
        '维护中': 'primary'
      }
      return statusMap[status] || 'default'
    },
    
    getNetworkTagType(networkType) {
      return networkType === '内网' ? 'primary' : 'warning'
    },
    
    formatLastChecked(lastChecked) {
      if (!lastChecked) return '未检查'
      const date = new Date(lastChecked)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return date.toLocaleDateString()
      }
    },
    
    goToDetail(id) {
      this.$router.push(`/detail/${id}`)
    },
    
    async deleteWebsite(id) {
      try {
        await Dialog.confirm({
          title: '删除确认',
          message: '确定要删除这个监控网站吗？'
        })
        
        await this.deleteWebsite(id)
        Toast.success('删除成功')
        await this.fetchStats() // 更新统计信息
      } catch (error) {
        if (error !== 'cancel') {
          Toast.fail('删除失败')
        }
      }
    },
    
    async checkWebsite(id) {
      try {
        Toast.loading('检查中...')
        await this.checkWebsite(id)
        Toast.success('检查完成')
        await this.fetchStats() // 更新统计信息
      } catch (error) {
        Toast.fail('检查失败')
      }
    }
  }
}
</script>

<style scoped>
.monitor-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content-container {
  padding-top: 46px; /* 导航栏高度 */
}

.stats-card {
  display: flex;
  background: white;
  margin: 16px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stats-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.stats-value.running {
  color: #07c160;
}

.stats-value.down {
  color: #ee0a24;
}

.stats-label {
  font-size: 12px;
  color: #969799;
}

.website-cell {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.website-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.network-tag, .status-tag {
  min-width: 40px;
  text-align: center;
}

.swipe-button {
  height: 100%;
}

.empty-container {
  padding: 60px 0;
}

.loading-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}
</style> 