<template>
  <div class="add-website-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="添加网站"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
      fixed
    />

    <div class="form-container">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.name"
            name="name"
            label="网站名称"
            placeholder="请输入网站名称"
            :rules="[{ required: true, message: '请输入网站名称' }]"
          />
          
          <van-field
            v-model="form.url"
            name="url"
            label="网站地址"
            placeholder="请输入网站URL（如：http://example.com）"
            :rules="[
              { required: true, message: '请输入网站地址' },
              { pattern: /^https?:\/\/.+/, message: '请输入正确的URL格式' }
            ]"
          />
          
          <van-field
            v-model="form.networkType"
            is-link
            readonly
            name="networkType"
            label="网络类型"
            placeholder="请选择网络类型"
            @click="showNetworkPicker = true"
            :rules="[{ required: true, message: '请选择网络类型' }]"
          />
          
          <van-field
            v-model="form.operatingHours.start"
            is-link
            readonly
            name="startTime"
            label="开始时间"
            placeholder="请选择开始时间"
            @click="showStartTimePicker = true"
          />
          
          <van-field
            v-model="form.operatingHours.end"
            is-link
            readonly
            name="endTime"
            label="结束时间"
            placeholder="请选择结束时间"
            @click="showEndTimePicker = true"
          />
        </van-cell-group>

        <div class="button-container">
          <van-button 
            round 
            block 
            type="primary" 
            native-type="submit"
            :loading="submitting"
          >
            添加网站
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 网络类型选择器 -->
    <van-popup v-model:show="showNetworkPicker" position="bottom">
      <van-picker
        :columns="networkTypes"
        @confirm="onNetworkConfirm"
        @cancel="showNetworkPicker = false"
      />
    </van-popup>

    <!-- 开始时间选择器 -->
    <van-popup v-model:show="showStartTimePicker" position="bottom">
      <van-time-picker
        v-model="startTime"
        title="选择开始时间"
        @confirm="onStartTimeConfirm"
        @cancel="showStartTimePicker = false"
      />
    </van-popup>

    <!-- 结束时间选择器 -->
    <van-popup v-model:show="showEndTimePicker" position="bottom">
      <van-time-picker
        v-model="endTime"
        title="选择结束时间"
        @confirm="onEndTimeConfirm"
        @cancel="showEndTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { Toast } from 'vant'

export default {
  name: 'AddWebsite',
  data() {
    return {
      form: {
        name: '',
        url: '',
        networkType: '内网',
        operatingHours: {
          start: '00:00',
          end: '23:59'
        }
      },
      submitting: false,
      showNetworkPicker: false,
      showStartTimePicker: false,
      showEndTimePicker: false,
      networkTypes: ['内网', '外网'],
      startTime: ['00', '00'],
      endTime: ['23', '59']
    }
  },
  methods: {
    ...mapActions(['addWebsite']),
    
    async onSubmit() {
      if (this.submitting) return
      
      this.submitting = true
      
      try {
        await this.addWebsite({
          ...this.form,
          status: '运行中',
          isActive: true
        })
        
        Toast.success('网站添加成功')
        this.$router.back()
      } catch (error) {
        Toast.fail(error.response?.data?.message || '添加失败')
      } finally {
        this.submitting = false
      }
    },
    
    onNetworkConfirm({ selectedValues }) {
      this.form.networkType = selectedValues[0]
      this.showNetworkPicker = false
    },
    
    onStartTimeConfirm() {
      this.form.operatingHours.start = `${this.startTime[0]}:${this.startTime[1]}`
      this.showStartTimePicker = false
    },
    
    onEndTimeConfirm() {
      this.form.operatingHours.end = `${this.endTime[0]}:${this.endTime[1]}`
      this.showEndTimePicker = false
    }
  }
}
</script>

<style scoped>
.add-website-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.form-container {
  padding-top: 46px; /* 导航栏高度 */
  padding: 60px 16px 16px;
}

.button-container {
  margin-top: 32px;
  padding: 0 16px;
}

.van-cell-group {
  margin-bottom: 16px;
}
</style> 