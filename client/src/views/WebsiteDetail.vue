<template>
  <div class="website-detail-page">
    <!-- 导航栏 -->
    <van-nav-bar
      :title="website?.name || '网站详情'"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
      fixed
    >
      <template #right>
        <van-button 
          type="primary" 
          size="small"
          @click="checkWebsite"
          :loading="checking"
        >
          检查
        </van-button>
      </template>
    </van-nav-bar>

    <div v-if="website" class="content-container">
      <!-- 状态卡片 -->
      <div class="status-card">
        <div class="status-header">
          <h3>{{ website.name }}</h3>
          <van-tag 
            :type="getStatusTagType(website.status)"
            size="large"
          >
            {{ website.status }}
          </van-tag>
        </div>
        
        <div class="status-info">
          <div class="info-item">
            <span class="info-label">网站地址</span>
            <span class="info-value">{{ website.url }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">网络类型</span>
            <van-tag 
              :type="getNetworkTagType(website.networkType)"
              size="small"
            >
              {{ website.networkType }}
            </van-tag>
          </div>
          
          <div class="info-item">
            <span class="info-label">运行时间</span>
            <span class="info-value">
              {{ website.operatingHours.start }} ~ {{ website.operatingHours.end }}
            </span>
          </div>
          
          <div class="info-item">
            <span class="info-label">最近检查</span>
            <span class="info-value">{{ formatLastChecked(website.lastChecked) }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">响应时间</span>
            <span class="info-value">{{ website.responseTime }}ms</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <van-cell-group inset>
        <van-cell 
          title="编辑网站信息"
          is-link
          @click="editWebsite"
        />
        <van-cell 
          title="删除网站"
          is-link
          @click="deleteWebsite"
          title-style="color: #ee0a24"
        />
      </van-cell-group>

      <!-- 监控历史 -->
      <van-cell-group inset title="监控历史">
        <van-cell>
          <div class="history-placeholder">
            <van-empty description="监控历史功能开发中" />
          </div>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 加载状态 -->
    <van-loading 
      v-else
      class="loading-center"
      size="24px"
    >
      加载中...
    </van-loading>

    <!-- 编辑弹窗 -->
    <van-popup 
      v-model:show="showEditDialog" 
      position="bottom"
      :style="{ height: '70%' }"
    >
      <div class="edit-form">
        <van-nav-bar
          title="编辑网站"
          left-text="取消"
          right-text="保存"
          @click-left="showEditDialog = false"
          @click-right="saveWebsite"
        />
        
        <van-form>
          <van-cell-group>
            <van-field
              v-model="editForm.name"
              label="网站名称"
              placeholder="请输入网站名称"
            />
            
            <van-field
              v-model="editForm.url"
              label="网站地址"
              placeholder="请输入网站URL"
            />
            
            <van-field
              v-model="editForm.networkType"
              is-link
              readonly
              label="网络类型"
              @click="showNetworkPicker = true"
            />
            
            <van-field
              v-model="editForm.operatingHours.start"
              is-link
              readonly
              label="开始时间"
              @click="showStartTimePicker = true"
            />
            
            <van-field
              v-model="editForm.operatingHours.end"
              is-link
              readonly
              label="结束时间"
              @click="showEndTimePicker = true"
            />
          </van-cell-group>
        </van-form>
      </div>
    </van-popup>

    <!-- 选择器 -->
    <van-popup v-model:show="showNetworkPicker" position="bottom">
      <van-picker
        :columns="networkTypes"
        @confirm="onNetworkConfirm"
        @cancel="showNetworkPicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showStartTimePicker" position="bottom">
      <van-time-picker
        v-model="startTime"
        @confirm="onStartTimeConfirm"
        @cancel="showStartTimePicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showEndTimePicker" position="bottom">
      <van-time-picker
        v-model="endTime"
        @confirm="onEndTimeConfirm"
        @cancel="showEndTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { Toast, Dialog } from 'vant'

export default {
  name: 'WebsiteDetail',
  data() {
    return {
      website: null,
      checking: false,
      showEditDialog: false,
      editForm: {
        name: '',
        url: '',
        networkType: '内网',
        operatingHours: {
          start: '00:00',
          end: '23:59'
        }
      },
      showNetworkPicker: false,
      showStartTimePicker: false,
      showEndTimePicker: false,
      networkTypes: ['内网', '外网'],
      startTime: ['00', '00'],
      endTime: ['23', '59']
    }
  },
  computed: {
    ...mapState(['websites'])
  },
  created() {
    this.loadWebsite()
  },
  methods: {
    ...mapActions(['fetchWebsites', 'updateWebsite', 'deleteWebsite', 'checkWebsite']),
    
    loadWebsite() {
      const id = this.$route.params.id
      this.website = this.websites.find(w => w._id === id)
      
      if (!this.website && this.websites.length === 0) {
        // 如果没有加载网站列表，先加载
        this.fetchWebsites().then(() => {
          this.website = this.websites.find(w => w._id === id)
        })
      }
    },
    
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '系统中断': 'danger',
        '被修改': 'warning',
        '维护中': 'primary'
      }
      return statusMap[status] || 'default'
    },
    
    getNetworkTagType(networkType) {
      return networkType === '内网' ? 'primary' : 'warning'
    },
    
    formatLastChecked(lastChecked) {
      if (!lastChecked) return '未检查'
      return new Date(lastChecked).toLocaleString()
    },
    
    async checkWebsite() {
      if (this.checking) return
      
      this.checking = true
      try {
        Toast.loading('检查中...')
        await this.checkWebsite(this.website._id)
        Toast.success('检查完成')
        
        // 重新加载网站信息
        await this.fetchWebsites()
        this.loadWebsite()
      } catch (error) {
        Toast.fail('检查失败')
      } finally {
        this.checking = false
      }
    },
    
    editWebsite() {
      this.editForm = {
        name: this.website.name,
        url: this.website.url,
        networkType: this.website.networkType,
        operatingHours: { ...this.website.operatingHours }
      }
      this.showEditDialog = true
    },
    
    async saveWebsite() {
      try {
        Toast.loading('保存中...')
        await this.updateWebsite({
          id: this.website._id,
          data: this.editForm
        })
        
        Toast.success('保存成功')
        this.showEditDialog = false
        
        // 重新加载网站信息
        await this.fetchWebsites()
        this.loadWebsite()
      } catch (error) {
        Toast.fail('保存失败')
      }
    },
    
    async deleteWebsite() {
      try {
        await Dialog.confirm({
          title: '删除确认',
          message: '确定要删除这个监控网站吗？'
        })
        
        Toast.loading('删除中...')
        await this.deleteWebsite(this.website._id)
        Toast.success('删除成功')
        this.$router.back()
      } catch (error) {
        if (error !== 'cancel') {
          Toast.fail('删除失败')
        }
      }
    },
    
    onNetworkConfirm({ selectedValues }) {
      this.editForm.networkType = selectedValues[0]
      this.showNetworkPicker = false
    },
    
    onStartTimeConfirm() {
      this.editForm.operatingHours.start = `${this.startTime[0]}:${this.startTime[1]}`
      this.showStartTimePicker = false
    },
    
    onEndTimeConfirm() {
      this.editForm.operatingHours.end = `${this.endTime[0]}:${this.endTime[1]}`
      this.showEndTimePicker = false
    }
  }
}
</script>

<style scoped>
.website-detail-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.content-container {
  padding-top: 46px; /* 导航栏高度 */
  padding: 60px 16px 16px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-header h3 {
  margin: 0;
  font-size: 18px;
  color: #323233;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  color: #969799;
  font-size: 14px;
}

.info-value {
  color: #323233;
  font-size: 14px;
  font-weight: 500;
}

.history-placeholder {
  padding: 40px 0;
}

.edit-form {
  height: 100%;
  background: #f7f8fa;
}

.loading-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.van-cell-group {
  margin-bottom: 16px;
}
</style> 