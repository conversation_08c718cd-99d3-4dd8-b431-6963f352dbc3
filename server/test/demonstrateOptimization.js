const mongoose = require('mongoose');
require('dotenv').config();

const Website = require('../models/Website');
const MonitorService = require('../services/monitorService');

// 连接数据库
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('已连接到MongoDB数据库');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
}

// 演示内容变化检测优化效果
async function demonstrateOptimization() {
  console.log('🚀 演示内容变化检测优化效果\n');
  
  try {
    // 获取一个测试网站
    const testWebsite = await Website.findOne();
    if (!testWebsite) {
      console.log('❌ 没有找到测试网站');
      return;
    }
    
    console.log(`📊 测试网站: ${testWebsite.name}`);
    console.log(`🔗 网址: ${testWebsite.url}\n`);
    
    // 清除之前的内容哈希，模拟首次检查
    await Website.findByIdAndUpdate(testWebsite._id, {
      contentHash: null
    });
    
    console.log('📋 测试场景说明:');
    console.log('1. 首次检查 - 需要进行AI内容检查');
    console.log('2. 重复检查 - 内容未变化，跳过AI检查');
    console.log('3. 对比性能差异\n');
    
    // 第一次检查（需要AI检查）
    console.log('🔍 === 第一次检查（首次检查，需要AI验证）===');
    const startTime1 = Date.now();
    let duration1 = 0;

    try {
      const result1 = await MonitorService.checkSingleWebsite(testWebsite);
      duration1 = Date.now() - startTime1;

      console.log(`✅ 检查完成`);
      console.log(`⏱️  耗时: ${duration1}ms`);
      console.log(`📊 状态: ${result1.status}`);
      console.log(`🔄 内容状态: ${result1.contentStatus}`);

    } catch (error) {
      duration1 = Date.now() - startTime1;
      console.log(`❌ 检查失败: ${error.message}`);
    }
    
    console.log('\n⏳ 等待3秒后进行第二次检查...\n');
    await sleep(3000);
    
    // 第二次检查（跳过AI检查）
    console.log('🔍 === 第二次检查（内容未变化，跳过AI检查）===');
    const startTime2 = Date.now();
    let duration2 = 0;

    try {
      // 重新获取网站信息（包含更新的哈希）
      const updatedWebsite = await Website.findById(testWebsite._id);
      const result2 = await MonitorService.checkSingleWebsite(updatedWebsite);
      duration2 = Date.now() - startTime2;

      console.log(`✅ 检查完成`);
      console.log(`⏱️  耗时: ${duration2}ms`);
      console.log(`📊 状态: ${result2.status}`);
      console.log(`🔄 内容状态: ${result2.contentStatus}`);

      // 计算性能提升
      const improvement = duration1 > 0 ? ((duration1 - duration2) / duration1 * 100).toFixed(1) : 0;

      console.log('\n📈 === 性能优化效果 ===');
      console.log(`🕐 首次检查耗时: ${duration1}ms (包含AI检查)`);
      console.log(`🕐 重复检查耗时: ${duration2}ms (跳过AI检查)`);
      console.log(`🚀 性能提升: ${improvement}% (节省 ${duration1 - duration2}ms)`);

      if (improvement > 50) {
        console.log('🎉 优化效果显著！大幅减少了重复AI调用');
      } else if (improvement > 20) {
        console.log('✨ 优化效果良好！有效减少了处理时间');
      } else {
        console.log('📝 优化效果一般，可能网络延迟影响了测试结果');
      }

    } catch (error) {
      duration2 = Date.now() - startTime2;
      console.log(`❌ 检查失败: ${error.message}`);
    }
    
    console.log('\n💡 === 优化原理说明 ===');
    console.log('1. 🔐 使用MD5哈希值检测内容变化');
    console.log('2. 📝 只有内容变化时才调用AI API');
    console.log('3. 💰 大幅减少API调用成本');
    console.log('4. ⚡ 显著提升检查速度');
    console.log('5. 🛡️  保持安全检查的完整性');
    
  } catch (error) {
    console.error('❌ 演示过程中出错:', error);
  }
}

// 等待函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 主函数
async function main() {
  await connectDB();
  await demonstrateOptimization();
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
}

// 运行演示
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { demonstrateOptimization };
