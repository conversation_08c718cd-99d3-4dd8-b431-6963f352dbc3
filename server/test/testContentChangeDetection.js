const mongoose = require('mongoose');
require('dotenv').config();

const Website = require('../models/Website');
const MonitorService = require('../services/monitorService');

// 连接数据库
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('已连接到MongoDB数据库');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
}

// 测试内容变化检测功能
async function testContentChangeDetection() {
  console.log('开始测试内容变化检测功能...\n');
  
  try {
    // 1. 获取一个测试网站
    const testWebsite = await Website.findOne();
    if (!testWebsite) {
      console.log('没有找到测试网站，请先添加一些网站');
      return;
    }
    
    console.log(`使用测试网站: ${testWebsite.name}`);
    console.log(`当前内容哈希: ${testWebsite.contentHash || '无'}\n`);
    
    // 2. 模拟相同内容的检查
    console.log('=== 测试1: 检查相同内容（应该跳过AI检查）===');
    const sameContent = `
      <html>
        <head><title>测试页面</title></head>
        <body>
          <h1>欢迎访问测试网站</h1>
          <p>这是一个测试页面，内容完全正常。</p>
          <p>没有任何违规内容。</p>
        </body>
      </html>
    `;
    
    const result1 = await MonitorService.checkWebsiteContent(testWebsite, sameContent);
    console.log('检查结果1:', {
      status: result1.status,
      contentChanged: result1.contentChanged,
      contentHash: result1.contentHash ? result1.contentHash.substring(0, 8) + '...' : '无'
    });
    
    // 更新网站的内容哈希
    await Website.findByIdAndUpdate(testWebsite._id, {
      contentHash: result1.contentHash
    });
    
    console.log('\n=== 测试2: 再次检查相同内容（应该跳过AI检查）===');
    const result2 = await MonitorService.checkWebsiteContent(testWebsite, sameContent);
    console.log('检查结果2:', {
      status: result2.status,
      contentChanged: result2.contentChanged,
      contentHash: result2.contentHash ? result2.contentHash.substring(0, 8) + '...' : '无'
    });
    
    // 3. 模拟内容变化的检查
    console.log('\n=== 测试3: 检查变化的内容（应该进行AI检查）===');
    const changedContent = `
      <html>
        <head><title>测试页面</title></head>
        <body>
          <h1>欢迎访问测试网站</h1>
          <p>这是一个测试页面，内容完全正常。</p>
          <p>没有任何违规内容。</p>
          <p>这是新增加的内容段落。</p>
        </body>
      </html>
    `;
    
    // 重新获取网站信息（包含更新的哈希）
    const updatedWebsite = await Website.findById(testWebsite._id);
    const result3 = await MonitorService.checkWebsiteContent(updatedWebsite, changedContent);
    console.log('检查结果3:', {
      status: result3.status,
      contentChanged: result3.contentChanged,
      contentHash: result3.contentHash ? result3.contentHash.substring(0, 8) + '...' : '无'
    });
    
    // 4. 测试短内容
    console.log('\n=== 测试4: 检查短内容（应该直接返回正常）===');
    const shortContent = `<html><body><p>短内容</p></body></html>`;
    const result4 = await MonitorService.checkWebsiteContent(updatedWebsite, shortContent);
    console.log('检查结果4:', {
      status: result4.status,
      contentChanged: result4.contentChanged,
      contentHash: result4.contentHash
    });
    
    // 5. 测试错误内容
    console.log('\n=== 测试5: 检查无效HTML内容 ===');
    const invalidContent = null;
    try {
      const result5 = await MonitorService.checkWebsiteContent(updatedWebsite, invalidContent);
      console.log('检查结果5:', {
        status: result5.status,
        contentChanged: result5.contentChanged
      });
    } catch (error) {
      console.log('检查结果5: 捕获到错误 -', error.message);
    }
    
    console.log('\n=== 测试完成 ===');
    console.log('总结:');
    console.log('- 相同内容检查：跳过AI检查，提高效率');
    console.log('- 内容变化检查：进行AI检查，确保安全');
    console.log('- 短内容检查：直接返回正常，节省资源');
    console.log('- 错误处理：正确处理异常情况');
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 测试实际网站检查流程
async function testRealWebsiteCheck() {
  console.log('\n开始测试实际网站检查流程...\n');
  
  try {
    // 获取一个测试网站
    const testWebsite = await Website.findOne();
    if (!testWebsite) {
      console.log('没有找到测试网站');
      return;
    }
    
    console.log(`测试网站: ${testWebsite.name} - ${testWebsite.url}`);
    
    // 执行两次检查，观察内容变化检测
    console.log('\n=== 第一次检查 ===');
    const result1 = await MonitorService.checkSingleWebsite(testWebsite);
    console.log('第一次检查完成');
    
    await sleep(2000); // 等待2秒
    
    console.log('\n=== 第二次检查（内容应该没有变化）===');
    const updatedWebsite = await Website.findById(testWebsite._id);
    const result2 = await MonitorService.checkSingleWebsite(updatedWebsite);
    console.log('第二次检查完成');
    
  } catch (error) {
    console.error('实际网站检查测试失败:', error);
  }
}

// 等待函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 主函数
async function main() {
  await connectDB();
  
  // 测试内容变化检测
  await testContentChangeDetection();
  
  // 测试实际网站检查
  await testRealWebsiteCheck();
  
  await mongoose.connection.close();
  console.log('\n数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testContentChangeDetection };
