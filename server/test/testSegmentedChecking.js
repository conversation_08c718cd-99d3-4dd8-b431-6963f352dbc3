const mongoose = require('mongoose');
require('dotenv').config();

const Website = require('../models/Website');
const MonitorService = require('../services/monitorService');

// 连接数据库
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('已连接到MongoDB数据库');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
}

// 测试真正的分段检查功能
async function testSegmentedChecking() {
  console.log('🔍 测试真正的分段检查功能\n');
  
  try {
    // 1. 测试短内容（不分段）
    console.log('📋 === 测试1: 短内容检查（不分段）===');
    const shortContent = '欢迎访问我们的网站，这里提供优质的服务和产品信息。';
    const segments1 = MonitorService.segmentContent(shortContent);
    console.log(`内容长度: ${shortContent.length}字符`);
    console.log(`分段结果: ${segments1.length}段`);
    
    const result1 = await MonitorService.checkContentWithAISegments(segments1, '测试网站1');
    console.log('检查结果:', {
      status: result1.status,
      violationDetails: result1.violationDetails,
      violationTypes: result1.violationTypes
    });
    
    // 2. 测试长内容（需要分段）
    console.log('\n📋 === 测试2: 长内容检查（多段）===');
    const longContent = `
      第一段内容：欢迎访问我们的官方网站。我们是一家专业的技术公司，致力于为客户提供最优质的服务。
      我们的团队拥有丰富的经验和专业的技能，能够满足各种复杂的业务需求。
      ${'我们提供全方位的解决方案，包括软件开发、系统集成、技术咨询等服务。'.repeat(20)}
      
      第二段内容：我们的产品线涵盖了多个领域，包括企业管理软件、移动应用开发、云计算服务等。
      每一个产品都经过严格的质量控制和测试，确保能够为客户带来最大的价值。
      ${'我们始终坚持以客户为中心的服务理念，不断创新和改进我们的产品和服务。'.repeat(20)}
      
      第三段内容：如果您对我们的产品和服务感兴趣，欢迎随时联系我们。
      我们的客服团队将为您提供专业的咨询和支持服务。
      ${'我们期待与您建立长期的合作关系，共同创造更美好的未来。'.repeat(20)}
    `;
    
    const segments2 = MonitorService.segmentContent(longContent);
    console.log(`内容长度: ${longContent.length}字符`);
    console.log(`分段结果: ${segments2.length}段`);
    segments2.forEach((segment, index) => {
      console.log(`  第${index + 1}段: ${segment.length}字符`);
    });
    
    console.log('\n开始分段检查...');
    const startTime = Date.now();
    const result2 = await MonitorService.checkContentWithAISegments(segments2, '测试网站2');
    const duration = Date.now() - startTime;
    
    console.log(`检查完成，耗时: ${duration}ms`);
    console.log('检查结果:', {
      status: result2.status,
      violationDetails: result2.violationDetails ? result2.violationDetails.substring(0, 200) + '...' : null,
      violationTypes: result2.violationTypes
    });
    
    // 3. 测试混合内容（部分正常，部分可能有问题）
    console.log('\n📋 === 测试3: 混合内容检查 ===');
    const mixedContent = `
      第一段：这是正常的企业介绍内容。我们公司成立于2020年，专注于提供优质的技术服务。
      ${'我们拥有专业的团队和先进的技术，能够为客户提供全方位的解决方案。'.repeat(15)}
      
      第二段：我们的服务包括软件开发、系统维护、技术咨询等多个方面。
      ${'客户满意度是我们最重要的指标，我们始终以客户需求为导向。'.repeat(15)}
      
      第三段：联系我们获取更多信息，我们将为您提供专业的服务和支持。
      ${'欢迎访问我们的官方网站了解更多详情。'.repeat(15)}
    `;
    
    const segments3 = MonitorService.segmentContent(mixedContent);
    console.log(`内容长度: ${mixedContent.length}字符`);
    console.log(`分段结果: ${segments3.length}段`);
    
    const result3 = await MonitorService.checkContentWithAISegments(segments3, '测试网站3');
    console.log('检查结果:', {
      status: result3.status,
      violationDetails: result3.violationDetails ? result3.violationDetails.substring(0, 200) + '...' : null,
      violationTypes: result3.violationTypes
    });
    
    // 4. 测试完整的网站内容检查流程
    console.log('\n📋 === 测试4: 完整网站检查流程 ===');
    const testWebsite = await Website.findOne();
    if (testWebsite) {
      console.log(`测试网站: ${testWebsite.name}`);
      
      // 清除内容哈希，强制进行AI检查
      await Website.findByIdAndUpdate(testWebsite._id, {
        contentHash: null
      });
      
      const mockLongHtml = `
        <html>
          <head><title>测试长内容网站</title></head>
          <body>
            <h1>欢迎访问我们的网站</h1>
            <div class="section1">
              <h2>公司介绍</h2>
              <p>我们是一家专业的技术公司，成立于2020年。</p>
              ${'<p>我们致力于为客户提供最优质的技术服务和解决方案。</p>'.repeat(30)}
            </div>
            <div class="section2">
              <h2>产品服务</h2>
              <p>我们的产品线涵盖多个技术领域。</p>
              ${'<p>包括软件开发、系统集成、云计算服务等。</p>'.repeat(30)}
            </div>
            <div class="section3">
              <h2>联系我们</h2>
              <p>如果您需要我们的服务，请随时联系我们。</p>
              ${'<p>我们将为您提供专业的咨询和技术支持。</p>'.repeat(30)}
            </div>
          </body>
        </html>
      `;
      
      console.log('开始完整网站内容检查...');
      const websiteStartTime = Date.now();
      const websiteResult = await MonitorService.checkWebsiteContent(testWebsite, mockLongHtml);
      const websiteDuration = Date.now() - websiteStartTime;
      
      console.log(`网站检查完成，耗时: ${websiteDuration}ms`);
      console.log('网站检查结果:', {
        status: websiteResult.status,
        contentChanged: websiteResult.contentChanged,
        contentHash: websiteResult.contentHash ? websiteResult.contentHash.substring(0, 8) + '...' : null,
        violationDetails: websiteResult.violationDetails ? websiteResult.violationDetails.substring(0, 100) + '...' : null
      });
    }
    
    console.log('\n📊 === 分段检查优势总结 ===');
    console.log('1. ✅ 真正的分段处理：每段独立检查，不遗漏');
    console.log('2. ✅ 精确定位：能够准确定位违规内容在哪一段');
    console.log('3. ✅ 全面覆盖：不再局限于前2000字符');
    console.log('4. ✅ 成本控制：使用本地Ollama，无API费用');
    console.log('5. ✅ 错误隔离：单段失败不影响其他段检查');
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 主函数
async function main() {
  await connectDB();
  await testSegmentedChecking();
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testSegmentedChecking };
