const mongoose = require('mongoose');
require('dotenv').config();

const Website = require('../models/Website');
const StatusHistory = require('../models/StatusHistory');
const MonitorService = require('../services/monitorService');

// 连接数据库
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('已连接到MongoDB数据库');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
}

// 测试智能警报功能
async function testSmartAlerts() {
  console.log('开始测试智能警报功能...\n');
  
  try {
    // 1. 获取一个测试网站
    const testWebsite = await Website.findOne();
    if (!testWebsite) {
      console.log('没有找到测试网站，请先添加一些网站');
      return;
    }
    
    console.log(`使用测试网站: ${testWebsite.name} (${testWebsite.url})`);
    console.log(`当前状态: ${testWebsite.status}`);
    console.log(`当前内容状态: ${testWebsite.contentStatus}\n`);
    
    // 2. 模拟状态变化测试
    console.log('=== 测试1: 模拟网站从正常变为异常 ===');
    await simulateStatusChange(testWebsite, '运行中', '系统中断');
    
    await sleep(2000); // 等待2秒
    
    console.log('\n=== 测试2: 模拟网站从异常恢复为正常 ===');
    await simulateStatusChange(testWebsite, '系统中断', '运行中');
    
    await sleep(2000); // 等待2秒
    
    console.log('\n=== 测试3: 模拟内容从正常变为违规 ===');
    await simulateContentStatusChange(testWebsite, '正常', '违规');
    
    await sleep(2000); // 等待2秒
    
    console.log('\n=== 测试4: 模拟内容从违规恢复为正常 ===');
    await simulateContentStatusChange(testWebsite, '违规', '正常');
    
    console.log('\n=== 测试5: 模拟重复异常状态（不应发送重复警报）===');
    await simulateStatusChange(testWebsite, '系统中断', '系统中断');
    
    console.log('\n测试完成！');
    
    // 3. 查看状态历史记录
    console.log('\n=== 状态变化历史记录 ===');
    const history = await StatusHistory.find({ websiteId: testWebsite._id })
      .sort({ createdAt: -1 })
      .limit(10);
    
    history.forEach((record, index) => {
      console.log(`${index + 1}. ${record.createdAt.toLocaleString()}`);
      console.log(`   状态变化: ${record.previousStatus} -> ${record.currentStatus}`);
      console.log(`   内容状态: ${record.previousContentStatus} -> ${record.currentContentStatus}`);
      console.log(`   变化类型: ${record.changeType}`);
      console.log(`   是否发送警报: ${record.alertSent}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 模拟状态变化
async function simulateStatusChange(website, fromStatus, toStatus) {
  console.log(`模拟状态变化: ${fromStatus} -> ${toStatus}`);
  
  // 更新网站状态
  await Website.findByIdAndUpdate(website._id, {
    previousStatus: fromStatus,
    status: toStatus
  });
  
  // 创建模拟的检查结果
  const mockCheckResult = {
    website: website,
    result: {
      websiteId: website._id,
      status: toStatus,
      previousStatus: fromStatus,
      contentStatus: website.contentStatus,
      previousContentStatus: website.contentStatus,
      statusChanged: fromStatus !== toStatus,
      contentStatusChanged: false,
      responseTime: 500,
      violationDetails: null,
      violationTypes: null
    }
  };
  
  // 记录状态变化
  if (fromStatus !== toStatus) {
    await MonitorService.recordStatusChange(website, {
      previousStatus: fromStatus,
      currentStatus: toStatus,
      previousContentStatus: website.contentStatus,
      currentContentStatus: website.contentStatus,
      statusChanged: true,
      contentStatusChanged: false,
      responseTime: 500,
      violationDetails: null,
      violationTypes: null
    });
  }
  
  // 触发智能警报检查
  await MonitorService.checkAndSendAlerts([mockCheckResult]);
}

// 模拟内容状态变化
async function simulateContentStatusChange(website, fromContentStatus, toContentStatus) {
  console.log(`模拟内容状态变化: ${fromContentStatus} -> ${toContentStatus}`);
  
  // 更新网站内容状态
  await Website.findByIdAndUpdate(website._id, {
    previousContentStatus: fromContentStatus,
    contentStatus: toContentStatus,
    violationDetails: toContentStatus === '违规' ? '测试违规内容' : null
  });
  
  // 创建模拟的检查结果
  const mockCheckResult = {
    website: website,
    result: {
      websiteId: website._id,
      status: website.status,
      previousStatus: website.status,
      contentStatus: toContentStatus,
      previousContentStatus: fromContentStatus,
      statusChanged: false,
      contentStatusChanged: fromContentStatus !== toContentStatus,
      responseTime: 500,
      violationDetails: toContentStatus === '违规' ? '测试违规内容' : null,
      violationTypes: toContentStatus === '违规' ? ['测试类型'] : null
    }
  };
  
  // 记录状态变化
  if (fromContentStatus !== toContentStatus) {
    await MonitorService.recordStatusChange(website, {
      previousStatus: website.status,
      currentStatus: website.status,
      previousContentStatus: fromContentStatus,
      currentContentStatus: toContentStatus,
      statusChanged: false,
      contentStatusChanged: true,
      responseTime: 500,
      violationDetails: toContentStatus === '违规' ? '测试违规内容' : null,
      violationTypes: toContentStatus === '违规' ? ['测试类型'] : null
    });
  }
  
  // 触发智能警报检查
  await MonitorService.checkAndSendAlerts([mockCheckResult]);
}

// 等待函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 主函数
async function main() {
  await connectDB();
  await testSmartAlerts();
  await mongoose.connection.close();
  console.log('数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testSmartAlerts };
