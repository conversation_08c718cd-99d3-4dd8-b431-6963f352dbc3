const mongoose = require('mongoose');
require('dotenv').config();

const Website = require('../models/Website');
const MonitorService = require('../services/monitorService');

// 连接数据库
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('已连接到MongoDB数据库');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
}

// 测试Ollama集成和内容分段
async function testOllamaIntegration() {
  console.log('🚀 测试Ollama集成和智能内容分段功能\n');
  
  try {
    // 1. 测试内容分段功能
    console.log('📋 === 测试1: 内容分段功能 ===');
    
    const shortContent = '这是一个短内容测试。';
    const segments1 = MonitorService.segmentContent(shortContent);
    console.log(`短内容分段结果 (${shortContent.length}字符):`, segments1.length, '段');
    
    const mediumContent = '这是一个中等长度的内容测试。'.repeat(50);
    const segments2 = MonitorService.segmentContent(mediumContent);
    console.log(`中等内容分段结果 (${mediumContent.length}字符):`, segments2.length, '段');
    segments2.forEach((segment, index) => {
      console.log(`  第${index + 1}段: ${segment.length}字符`);
    });
    
    const longContent = '这是一个很长的内容测试，用来验证分段功能是否正常工作。'.repeat(100);
    const segments3 = MonitorService.segmentContent(longContent);
    console.log(`长内容分段结果 (${longContent.length}字符):`, segments3.length, '段');
    segments3.forEach((segment, index) => {
      console.log(`  第${index + 1}段: ${segment.length}字符`);
    });
    
    // 2. 测试Ollama API连接
    console.log('\n🔗 === 测试2: Ollama API连接 ===');
    
    try {
      const testResponse = await fetch('http://10.10.246.215:11434/api/tags');
      if (testResponse.ok) {
        const models = await testResponse.json();
        console.log('✅ Ollama服务连接成功');
        console.log('可用模型:', models.models?.map(m => m.name) || '无法获取模型列表');
      } else {
        console.log('❌ Ollama服务连接失败:', testResponse.status);
      }
    } catch (error) {
      console.log('❌ Ollama服务连接错误:', error.message);
    }
    
    // 3. 测试AI内容检查
    console.log('\n🤖 === 测试3: AI内容检查 ===');
    
    const normalContent = '欢迎访问我们的官方网站。这里提供最新的产品信息和服务介绍。我们致力于为用户提供优质的体验。';
    console.log('测试正常内容...');
    
    try {
      const result1 = await MonitorService.checkContentWithAI(normalContent);
      console.log('正常内容检查结果:', {
        status: result1.status,
        violationDetails: result1.violationDetails ? result1.violationDetails.substring(0, 100) + '...' : null,
        violationTypes: result1.violationTypes
      });
    } catch (error) {
      console.log('正常内容检查失败:', error.message);
    }
    
    // 4. 测试完整的网站内容检查流程
    console.log('\n🌐 === 测试4: 完整网站内容检查流程 ===');
    
    const testWebsite = await Website.findOne();
    if (testWebsite) {
      console.log(`测试网站: ${testWebsite.name}`);
      
      // 模拟HTML内容
      const mockHtmlContent = `
        <html>
          <head><title>测试网站</title></head>
          <body>
            <h1>欢迎访问测试网站</h1>
            <p>这是一个正常的网站内容。我们提供各种服务和产品信息。</p>
            <p>用户可以在这里找到他们需要的信息。我们承诺提供安全、可靠的服务。</p>
            <div>更多内容...</div>
            ${'<p>这是重复的段落内容，用于测试长文本处理。</p>'.repeat(20)}
          </body>
        </html>
      `;
      
      try {
        const result = await MonitorService.checkWebsiteContent(testWebsite, mockHtmlContent);
        console.log('网站内容检查结果:', {
          status: result.status,
          contentChanged: result.contentChanged,
          contentHash: result.contentHash ? result.contentHash.substring(0, 8) + '...' : null,
          violationDetails: result.violationDetails ? result.violationDetails.substring(0, 100) + '...' : null
        });
      } catch (error) {
        console.log('网站内容检查失败:', error.message);
      }
    } else {
      console.log('没有找到测试网站');
    }
    
    // 5. 性能对比测试
    console.log('\n⚡ === 测试5: 性能对比 ===');
    
    const performanceTestContent = '这是性能测试内容。'.repeat(200);
    
    console.log('开始性能测试...');
    const startTime = Date.now();
    
    try {
      await MonitorService.checkContentWithAI(performanceTestContent);
      const duration = Date.now() - startTime;
      console.log(`✅ Ollama检查耗时: ${duration}ms`);
      console.log('💰 成本优势: 使用本地Ollama，无API调用费用');
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ Ollama检查失败 (${duration}ms):`, error.message);
    }
    
    console.log('\n📊 === 优化总结 ===');
    console.log('1. ✅ 智能内容分段: 开头、中间、结尾三段式处理');
    console.log('2. ✅ 本地Ollama集成: 使用qwen2.5:32b模型');
    console.log('3. ✅ 成本优化: 无API调用费用');
    console.log('4. ✅ 内容覆盖: 不再局限于前2000字符');
    console.log('5. ✅ 错误处理: 完善的异常处理和降级策略');
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 测试Ollama服务状态
async function testOllamaService() {
  console.log('\n🔧 === Ollama服务状态检查 ===');
  
  try {
    // 检查服务是否运行
    const healthResponse = await fetch('http://10.10.246.215:11434/api/tags', {
      method: 'GET',
      timeout: 5000
    });
    
    if (healthResponse.ok) {
      const data = await healthResponse.json();
      console.log('✅ Ollama服务正常运行');
      console.log('📋 可用模型:');
      data.models?.forEach(model => {
        console.log(`  - ${model.name} (${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)`);
      });
    } else {
      console.log('❌ Ollama服务响应异常:', healthResponse.status);
    }
  } catch (error) {
    console.log('❌ 无法连接到Ollama服务:', error.message);
    console.log('💡 请确保:');
    console.log('   1. Ollama服务在10.10.246.215:11434运行');
    console.log('   2. qwen2.5:32b模型已安装');
    console.log('   3. 网络连接正常');
  }
}

// 主函数
async function main() {
  await connectDB();
  
  // 检查Ollama服务
  await testOllamaService();
  
  // 测试集成功能
  await testOllamaIntegration();
  
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testOllamaIntegration, testOllamaService };
