const express = require('express');
const router = express.Router();
const Website = require('../models/Website');
const MonitorService = require('../services/monitorService');

// 获取所有网站监控信息
router.get('/websites', async (req, res) => {
  try {
    const websites = await Website.find().sort({ createdAt: -1 });
    res.json({
      success: true,
      data: websites
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取网站信息失败',
      error: error.message
    });
  }
});

// 添加新网站
router.post('/websites', async (req, res) => {
  try {
    const website = new Website(req.body);
    await website.save();
    res.status(201).json({
      success: true,
      data: website,
      message: '网站添加成功'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '网站添加失败',
      error: error.message
    });
  }
});

// 更新网站信息
router.put('/websites/:id', async (req, res) => {
  try {
    const website = await Website.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!website) {
      return res.status(404).json({
        success: false,
        message: '网站未找到'
      });
    }
    res.json({
      success: true,
      data: website,
      message: '网站信息更新成功'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '网站信息更新失败',
      error: error.message
    });
  }
});

// 删除网站
router.delete('/websites/:id', async (req, res) => {
  try {
    const website = await Website.findByIdAndDelete(req.params.id);
    if (!website) {
      return res.status(404).json({
        success: false,
        message: '网站未找到'
      });
    }
    res.json({
      success: true,
      message: '网站删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '网站删除失败',
      error: error.message
    });
  }
});

// 手动检查单个网站
router.post('/check/:id', async (req, res) => {
  try {
    const website = await Website.findById(req.params.id);
    if (!website) {
      return res.status(404).json({
        success: false,
        message: '网站未找到'
      });
    }
    
    const result = await MonitorService.checkSingleWebsite(website);
    res.json({
      success: true,
      data: result,
      message: '网站检查完成'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '网站检查失败',
      error: error.message
    });
  }
});

// 获取监控统计信息
router.get('/stats', async (req, res) => {
  try {
    const totalWebsites = await Website.countDocuments();
    const runningWebsites = await Website.countDocuments({ status: '运行中' });
    const downWebsites = await Website.countDocuments({ status: '系统中断' });
    const internalWebsites = await Website.countDocuments({ networkType: '内网' });
    const externalWebsites = await Website.countDocuments({ networkType: '外网' });

    res.json({
      success: true,
      data: {
        total: totalWebsites,
        running: runningWebsites,
        down: downWebsites,
        internal: internalWebsites,
        external: externalWebsites,
        uptime: totalWebsites > 0 ? ((runningWebsites / totalWebsites) * 100).toFixed(2) : 100
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
});

// 测试警报功能


module.exports = router; 