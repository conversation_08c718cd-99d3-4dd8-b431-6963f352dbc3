const express = require('express');
const router = express.Router();
const User = require('../models/User');
const UserService = require('../services/userService');

const userService = new UserService();

// 获取所有用户
router.get('/users', async (req, res) => {
  try {
    const { department, isActive, receiveAlerts } = req.query;
    const filter = {};
    
    if (department) filter.department = department;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    if (receiveAlerts !== undefined) filter.receiveAlerts = receiveAlerts === 'true';
    
    const users = await userService.getAllUsers(filter);
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 根据工号获取用户
router.get('/users/:employeeId', async (req, res) => {
  try {
    const user = await User.findOne({ employeeId: req.params.employeeId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户未找到'
      });
    }
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
});

// 添加用户
router.post('/users', async (req, res) => {
  try {
    const { employeeId, name, department, position, email, phone, corpUserId, userId, alertTypes } = req.body;
    
    // 检查工号是否已存在
    const existingUser = await User.findOne({ employeeId });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '工号已存在'
      });
    }
    
    const userData = {
      employeeId,
      name,
      department: department || '',
      position: position || '',
      email: email || '',
      phone: phone || '',
      corpUserId: corpUserId || '',
      userId: userId || '',
      alertTypes: alertTypes || ['website_down', 'website_error']
    };
    
    const user = await userService.createUser(userData);
    res.status(201).json({
      success: true,
      data: user,
      message: '用户创建成功'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
});

// 更新用户
router.put('/users/:employeeId', async (req, res) => {
  try {
    const user = await userService.updateUser(req.params.employeeId, req.body);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户未找到'
      });
    }
    res.json({
      success: true,
      data: user,
      message: '用户信息更新成功'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '更新用户信息失败',
      error: error.message
    });
  }
});

// 删除用户
router.delete('/users/:employeeId', async (req, res) => {
  try {
    await userService.deleteUser(req.params.employeeId);
    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
});

// 批量更新用户警报设置
router.put('/users/alert-settings/batch', async (req, res) => {
  try {
    const { employeeIds, alertSettings } = req.body;
    
    if (!employeeIds || !Array.isArray(employeeIds) || employeeIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '工号列表不能为空'
      });
    }
    
    const updateCount = await userService.updateAlertSettings(employeeIds, alertSettings);
    res.json({
      success: true,
      data: { updateCount },
      message: `成功更新 ${updateCount} 个用户的警报设置`
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: '批量更新警报设置失败',
      error: error.message
    });
  }
});

// 获取接收特定类型警报的用户
router.get('/users/alert/:alertType', async (req, res) => {
  try {
    const users = await userService.getAlertUsers(req.params.alertType);
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取警报用户失败',
      error: error.message
    });
  }
});

// 根据企业用户ID获取用户详情
router.post('/users/detail/corp', async (req, res) => {
  try {
    const { corpUserIds } = req.body;
    
    if (!corpUserIds || !Array.isArray(corpUserIds)) {
      return res.status(400).json({
        success: false,
        message: '企业用户ID列表格式错误'
      });
    }
    
    const users = await userService.getUsersDetail({ corpUserIds });
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
});

// 根据工号获取用户详情
router.post('/users/detail/employee', async (req, res) => {
  try {
    const { employeeIds } = req.body;
    
    if (!employeeIds || !Array.isArray(employeeIds)) {
      return res.status(400).json({
        success: false,
        message: '工号列表格式错误'
      });
    }
    
    const users = await userService.getUsersByEmployeeIds(employeeIds);
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
});

module.exports = router; 