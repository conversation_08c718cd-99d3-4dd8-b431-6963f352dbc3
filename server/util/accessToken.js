// accesstoken.js

const RedisService = require('./redis.js');


class AccessTokenManager {
  constructor(redisPrefix = 'gk_data_query_assist:') {
    
   
    this.TOKEN_KEY = 'accessToken';
    this.TOKEN_EXPIRY = 10; // Token 有效期，单位秒
  }


  async getAccessToken() {
   
    try {
      // 尝试从 Redis 获取 accessToken
      
      const redisService = new RedisService();
      await redisService.connect();
      let accessToken = await redisService.getValue(this.TOKEN_KEY);
   
      // console.log("accessToken ",accessToken)
      if (accessToken) {
        //  console.log('Access token retrieved from Redis');
        return accessToken;
      }
      //console.log("accessToken ",accessToken)
      // 如果 Redis 中没有 accessToken，则通过 API 获取
      console.log('Access token not found in Redis, fetching from API');
      accessToken = await this.fetchNewToken();
      console.log("accessToken ",accessToken)
      // 将新获取的 accessToken 存储到 Redis
      await redisService.setValue(this.TOKEN_KEY, accessToken, this.TOKEN_EXPIRY);
      await redisService.disconnect();
      // console.log('New access token fetched and stored in Redis ');
      return accessToken;
    } catch (error) {
      console.error('Error getting access token:', error);
      throw error;
    }finally{
    
    }
  }

  async fetchNewToken() {
    const response = await fetch('https://open.welink.huaweicloud.com/api/auth/v2/tickets', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        // 这里需要填写实际的认证信息
        // client_id: '20240813092941730906392',
        // client_secret: 'a40fe27f-a112-4850-9dc0-b88c859b3ee0'

        client_id: '20241005153857051785219',
        client_secret: '9cd22c5a-12c4-4ee7-aa01-7e4756c46a91'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.access_token; // 假设返回的 JSON 中包含 access_token 字段
  }
}

module.exports = AccessTokenManager;