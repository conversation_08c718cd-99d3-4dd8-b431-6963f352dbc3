const redis = require('redis');

let redisConfig;

if(process.env.NODE_ENV === 'production'){
  redisConfig = {
    url: 'redis://redis:6379'
  };
}else{
  redisConfig = {
    socket: {
      host: '************',
      port: 6379
    }
  };
}

class RedisService {
  constructor() {
    console.log("redisConfig", redisConfig);
    // 使用对象形式的配置
    this.client = redis.createClient(redisConfig);
    this.client.on('error', (err) => console.log('Redis Client Error,we will try to reconnect redis', err));
    this.PREFIX = 'gk_data_query_assist:';
    this.isConnected = false;
  }

  async connect() {
  
    if (!this.isConnected) {
      await this.client.connect();
      this.isConnected = true;
      console.log('Redis client connected');
    }
  }

  async disconnect() {
    if (this.isConnected) {
      await this.client.quit();
      this.isConnected = false;
      console.log('Redis client disconnected');
    }
  }

  async setValue(key, value, expireTime) {
    try {
      await this.connect();
      await this.client.set(this.PREFIX + key, JSON.stringify(value), {
        EX: expireTime
      });
      // console.log(`Successfully set ${key}`);
    } catch (err) {
      console.error(`Error setting ${key}:`, err);
      throw err;
    }
  }

  async getValue(key) {
    try {
      await this.connect();
      const value = await this.client.get(this.PREFIX + key);
      return value ? JSON.parse(value) : null;
    } catch (err) {
      console.error(`Error getting ${key}:`, err);
      return null;
    }
  }

  // 添加其他方法，如删除键
  async deleteKey(key) {
    try {
      await this.connect();
      await this.client.del(this.PREFIX + key);
      console.log(`Successfully deleted ${key}`);
    } catch (err) {
      console.error(`Error deleting ${key}:`, err);
      throw err;
    }
  }
}

// 创建一个单例实例

module.exports = RedisService;