// form-service.js

const AccessTokenManager = require('./accessToken.js');
class UserService {
  constructor() {
    this.accessTokenManager = new AccessTokenManager();
  }

  async getAccessToken() {
    return await this.accessTokenManager.getAccessToken();
  }

  async fetchWithAuth(url, method, params) {
    const accessToken = await this.getAccessToken();

    let fullUrl = url;
    let options = {
      method: method,
      headers: {
        'Accept-Charset': 'UTF-8',
        'Content-Type': 'application/json',
        'x-wlk-Authorization': accessToken,
        lang: "zh"
      }
    };

    if (method === 'GET' && params) {
      const queryString = new URLSearchParams(params).toString();
      fullUrl = `${url}?${queryString}`;

    } else if (method === 'POST' && params) {
      console.log(params)
      options.body = JSON.stringify(params);

    }

    const response = await fetch(fullUrl, options);

    if (response.status !== 200) {
      throw new Error(`HTTP error! status: ${response.statusText}`);
    }

    let json = await response.json();
  
    if (json.code !== "0")
      throw json.message;
    else
      if (json.data)
        return json.data;
      else
        return json;
  }

  async getChildDept(params) {
    const url = 'https://open.welink.huaweicloud.com/api/contact/v2/department/list'
    return await this.fetchWithAuth(url, 'GET', params);
  }
  async getUsersDetail(params) {
    // console.log("getUsersDetail")
    const url = "https://open.welink.huaweicloud.com/api/auth/v1/userids/"
   
    return await this.fetchWithAuth(url, 'POST', params);
  }

  async getDeptDetail(params) {

    const url = 'https://open.welink.huaweicloud.com/api/contact/v2/department/detail';
    return await this.fetchWithAuth(url, 'GET', params);
  }
  async getUserDetail(params) {
    const url = 'https://open.welink.huaweicloud.com/api/contact/v2/user/detail';
    return await this.fetchWithAuth(url, 'POST', params);
  }
  async simplelist(params) {
    const url = 'https://open.welink.huaweicloud.com/api/contact/v1/user/simplelist';
    return await this.fetchWithAuth(url, 'GET', params);
  }

  async updateBatchUser(params) {
    const url = 'https://open.welink.huaweicloud.com/api/contact/v1/user/batch/update';
    const batchSize = 100;
    const results = [];
    const batches = this.splitIntoBatches(params, batchSize);

    for (const batch of batches) {
      const result = await this.fetchWithAuth(url, 'POST',{personInfos:batch} );
      results.push(result);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return results;
  }
  //
  splitIntoBatches(array, size) {
    const batches = [];
    for (let i = 0; i < array.length; i += size) {
      batches.push(array.slice(i, i + size));
    }
    return batches;
  }

  async getAllUsers(departmentId) {
    const allUsers = [];
    await this.recursiveGetUsers(departmentId, allUsers);
    
    return allUsers;
  }

  async recursiveGetUsers(departmentId, allUsers) {
    // 获取当前部门的所有用户
    // console.log("aaab")
    await this.getAllUsersInDepartment(departmentId, allUsers);

    // 获取所有子部门
    await this.getAllChildDepartments(departmentId, async (childDept) => {
      // console.log(childDept.corpDeptCode)
      await this.recursiveGetUsers(childDept.corpDeptCode, allUsers);
    });
  }

  async getAllUsersInDepartment(departmentId, allUsers) {
    let pageNo = 1;
    let hasMore = true;

    while (hasMore) {
      const users = await this.simplelist({
        corpDeptCode: departmentId,
        pageNo: pageNo,
        pageSize: 50
      });

      allUsers.push(...users);

      if (users.length < 50) {
        hasMore = false;
      } else {
        pageNo++;
      }
    }
  }

  async getAllChildDepartments(departmentId, callback) {
    let pageNo = 1;
    let hasMore = true;
    console.log(departmentId)
    while (hasMore) {
      const childDepartments = await this.getChildDept({
        corpDeptCode: departmentId,
        pageNo: pageNo,
        pageSize: 50
      });

      for (const childDept of childDepartments) {
        await callback(childDept);
      }

      if (childDepartments.length < 50) {
        hasMore = false;
      } else {
        pageNo++;
      }
    }
  }
}

const userService = new UserService();
module.exports = userService;