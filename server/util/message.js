// form-service.js

const AccessTokenManager = require('./accessToken.js');

class MessageService {
  constructor() {
    this.accessTokenManager = new AccessTokenManager();
    this.baseUrl = 'https://open.welink.huaweicloud.com/api/messages/v1/';
  }

 

  async getAccessToken() {
    return await this.accessTokenManager.getAccessToken();
  }

  async fetchWithAuth(url, method, body) {
    const accessToken = await this.getAccessToken();
    const response = await fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-wlk-Authorization': accessToken
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    let json= await response.json();
    
    if(json.code !=="0")
      {
       throw json.message;
      }
      return json;
  }
  async sendText(params){
    const url = `${this.baseUrl}/text`;
    return await this.fetchWithAuth(url, 'POST', params);
  }
}

module.exports = MessageService;
