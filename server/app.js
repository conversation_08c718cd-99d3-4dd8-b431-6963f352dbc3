const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const cron = require('node-cron');
require('dotenv').config();

const monitorRoutes = require('./routes/monitor');
const userRoutes = require('./routes/user');
const MonitorService = require('./services/monitorService');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 连接MongoDB
mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

mongoose.connection.on('connected', () => {
  console.log('已连接到MongoDB数据库');
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB连接错误:', err);
});

// 路由
app.use('/api/monitor', monitorRoutes);
app.use('/api', userRoutes);

// 定时监控任务 - 每5分钟执行一次
cron.schedule('* * * * *', async () => {
  console.log('执行网站监控检查...');
  await MonitorService.checkAllWebsites();
});

app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
}); 