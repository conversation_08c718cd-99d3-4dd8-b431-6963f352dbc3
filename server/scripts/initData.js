const mongoose = require('mongoose');
require('dotenv').config();

const Website = require('../models/Website');
const User = require('../models/User');

// 状态映射函数
function mapStatus(originalStatus) {
  const statusMap = {
    '运行中': '运行中',
    '错误': '系统中断', 
    '暂停中': '维护中',
    '被修改': '被修改'
  };
  return statusMap[originalStatus] || '系统中断';
}

// 从ISODate提取时间
function extractTime(isoDate) {
  if (!isoDate) return '00:00';
  const date = new Date(isoDate);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}

// 真实的监控数据
const realWebsiteData = [
  {
    name: "广科官网",
    linkAddress: "https://www.gdit.edu.cn/",
    monitorType: "内网",
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "图书馆", 
    linkAddress: "https://library.gdit.edu.cn/",
    monitorType: "内网",
    status: "被修改",
    isOpen: true,
    startTime: "2021-01-31T23:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "OA办公",
    linkAddress: "https://oa.gdit.edu.cn",
    monitorType: "内网", 
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "财务平台",
    linkAddress: "http://cwwb.gdit.edu.cn/",
    monitorType: "内网",
    status: "错误", 
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "人事",
    linkAddress: "http://hr.gdit.edu.cn/",
    monitorType: "内网",
    status: "错误",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z", 
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "迎新",
    linkAddress: "http://yx.gdit.edu.cn/GditYX/Web",
    monitorType: "内网",
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "网办大厅",
    linkAddress: "https://ids.gdit.edu.cn/authserver/login?service=https://wbdt.gdit.edu.cn/shiro-cas",
    monitorType: "内网",
    status: "运行中",
    isOpen: true,
    startTime: "2021-02-01T00:00:00.000Z",
    endTime: "2021-02-01T14:00:00.000Z"
  },
  {
    name: "考勤",
    linkAddress: "https://churu1.gdit.edu.cn/sign_admin/",
    monitorType: "内网",
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "教务",
    linkAddress: "http://jw.gdit.edu.cn/jwglxt/xtgl/login_slogin.html",
    monitorType: "内网",
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "科研系统",
    linkAddress: "http://ky.gdit.edu.cn",
    monitorType: "内网",
    status: "运行中",
    isOpen: true,
    startTime: "2021-02-01T00:00:00.000Z",
    endTime: "2021-02-01T15:00:00.000Z"
  },
  {
    name: "学工系统",
    linkAddress: "https://xg.gdit.edu.cn",
    monitorType: "内网",
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "出入登记",
    linkAddress: "https://churu1.gdit.edu.cn/churudengji/admin/",
    monitorType: "内网",
    status: "运行中",
    isOpen: true,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "资产系统",
    linkAddress: "http://************:8080/ZCGL",
    monitorType: "内网",
    status: "暂停中",
    isOpen: false,
    startTime: "2021-01-31T23:00:00.000Z",
    endTime: "2021-02-01T15:00:00.000Z"
  },
  {
    name: "项目班管理后台",
    linkAddress: "http://xssw.gdit.edu.cn/pcm_admin/#/login?redirect=%2F",
    monitorType: "内网",
    status: "错误",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "人脸服务平台",
    linkAddress: "https://renlian.gdit.edu.cn",
    monitorType: "内网", 
    status: "运行中",
    isOpen: true,
    startTime: "2021-01-31T23:00:00.000Z",
    endTime: "2021-02-01T15:00:00.000Z"
  },
  {
    name: "2023质量工程",
    linkAddress: "https://zlgc.gdit.edu.cn/wstModel/2023zlgc/index.html",
    monitorType: "外网",
    status: "运行中",
    isOpen: true,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "思政课程",
    linkAddress: "https://zlgc.gdit.edu.cn/wstModel/2023kcszsf/index.html",
    monitorType: "外网",
    status: "运行中",
    isOpen: true,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "双高验收网站",
    linkAddress: "https://zlgc.gdit.edu.cn/WaiModel/sgjh/",
    monitorType: "外网",
    status: "运行中", 
    isOpen: true,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "2024省教改验收",
    linkAddress: "https://zlgc.gdit.edu.cn/wstModel/2024sjgxmyszl/index.html",
    monitorType: "内网",
    status: "错误",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  },
  {
    name: "创新强校评审工作",
    linkAddress: "https://zlgc.gdit.edu.cn/WaiModel/2024cxqxkh/",
    monitorType: "内网",
    status: "错误",
    isOpen: false,
    startTime: "2021-01-31T16:00:00.000Z",
    endTime: "2021-02-01T15:59:00.000Z"
  }
];

// 将原始数据转换为我们的数据模型格式
const transformedWebsites = realWebsiteData.map(item => ({
  name: item.name,
  url: item.linkAddress,
  networkType: item.monitorType,
  status: mapStatus(item.status),
  operatingHours: {
    start: extractTime(item.startTime),
    end: extractTime(item.endTime)
  },
  isActive: item.isOpen,
  responseTime: Math.floor(Math.random() * 500) + 100, // 随机响应时间
  lastChecked: new Date()
}));

// 简化的用户数据 - 只包含工号和姓名
const realUsers = [

  { employeeId: "10347", name: "胡玉贵" },
  { employeeId: "11918", name: "方开红" },
  { employeeId: "10276", name: "李柳霞" },
  { employeeId: "11821", name: "庞钦" },
  { employeeId: "10268", name: "黄建斌" },
  { employeeId: "11890", name: "苏伟铭" },
  { employeeId: "10259", name: "曾忆安" },
  { employeeId: "10266", name: "王慧" },
  { employeeId: "10277", name: "薛阳" },
  { employeeId: "10263", name: "黄斌" },
  { employeeId: "11701", name: "何磊" },
  { employeeId: "0104191034", name: "李松林" },
  { employeeId: "10260", name: "朱光迅" },
  { employeeId: "10262", name: "黄华君" },
  { employeeId: "10279", name: "李飞龙" },
  { employeeId: "12153", name: "黎浩" },
  { employeeId: "10699", name: "万畅" },
  { employeeId: "10169", name: "李建青" },
  { employeeId: "12254", name: "袁竟乘" }
];

// 转换为我们的用户模型格式
const transformedUsers = realUsers.map(user => ({
  employeeId: user.employeeId,
  name: user.name,

}));

async function initializeData() {
  try {
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || '************************************************************', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('已连接到MongoDB数据库');

    // 清除现有数据
    await Website.deleteMany({});
    await User.deleteMany({});
    console.log('已清除现有数据');

    // 插入网站数据
    await Website.insertMany(transformedWebsites);
    console.log(`已插入 ${transformedWebsites.length} 条网站数据`);

    // 插入用户数据
    await User.insertMany(transformedUsers);
    console.log(`已插入 ${transformedUsers.length} 条用户数据`);

    console.log('\n✅ 真实数据初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据初始化失败:', error);
  } finally {
    mongoose.connection.close();
  }
}

// 运行初始化
initializeData(); 