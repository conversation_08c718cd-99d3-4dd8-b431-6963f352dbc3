const User = require('../models/User');

class UserService {


  /**
   * 获取所有用户
   * @param {Object} filter - 过滤条件
   * @returns {Array} 用户列表
   */
  async getAllUsers(filter = {}) {
    try {
      const users = await User.find(filter).sort({ createdAt: -1 });
      return users;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      return [];
    }
  }
}

module.exports = UserService; 