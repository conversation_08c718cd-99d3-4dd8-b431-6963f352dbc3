const axios = require('axios');
const crypto = require('crypto');
const Website = require('../models/Website');
const StatusHistory = require('../models/StatusHistory');
const MessageService = require('../util/message');
const userService = require('../util/user');
const cheerio = require('cheerio');

// 如果Node.js版本小于18，需要安装node-fetch
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  fetch = require('node-fetch');
}

class MonitorService {
  // 检查所有网站
  static async checkAllWebsites() {
    try {
      const websites = await Website.find({  });
      console.log(`开始检查 ${websites.length} 个网站...`);
      
      const checkPromises = websites.map(website => this.checkSingleWebsite(website));
      const results = await Promise.allSettled(checkPromises);
      
      // 收集检查结果
      const checkResults = results.map((result, index) => ({
        website: websites[index],
        result: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }));

      // 检查是否需要发送警报
      await this.checkAndSendAlerts(checkResults);
      
      console.log('所有网站检查完成');
    } catch (error) {
      console.error('批量检查网站时出错:', error);
    }
  }

  // 检查单个网站
  static async checkSingleWebsite(website) {
    const startTime = Date.now();
    let status = '系统中断';
    let responseTime = 0;
    let contentStatus = '正常';
    let violationDetails = null;
    let violationTypes = null;

    try {
      console.log(`检查网站: ${website.name} - ${website.url}`);
      
      const response = await axios.get(website.url, {
        timeout: 10000, // 10秒超时
        validateStatus: function (status) {
          return status < 500; // 状态码小于500都认为是成功
        }
      });

      responseTime = Date.now() - startTime;

      if (response.status >= 200 && response.status < 500) {
        status = '运行中';
        
        // 进行内容检查（优化版：先检测变化再AI检查）
        try {
          const contentCheckResult = await this.checkWebsiteContent(website, response.data);
          contentStatus = contentCheckResult.status;
          violationDetails = contentCheckResult.violationDetails;
          violationTypes = contentCheckResult.violationTypes;

          // 更新内容哈希
          if (contentCheckResult.contentHash) {
            await Website.findByIdAndUpdate(website._id, {
              contentHash: contentCheckResult.contentHash,
              lastContentCheck: new Date()
            });
          }

          console.log(`${website.name} 内容检查结果: ${contentStatus}`);
          if (contentCheckResult.contentChanged) {
            console.log(`${website.name} 内容发生变化，已进行AI检查`);
          } else {
            console.log(`${website.name} 内容未变化，跳过AI检查`);
          }

          if (violationDetails) {
            console.log(`违规详情: ${violationDetails}`);
            console.log(`违规类型: ${JSON.stringify(violationTypes)}`);
          }
        } catch (contentError) {
          console.error(`${website.name} 内容检查失败:`, contentError);
          contentStatus = '检查失败';
        }
        
      }  else {
        status = '系统中断';
      }

      console.log(`${website.name} 检查结果: ${status}, 响应时间: ${responseTime}ms`);

    } catch (error) {
      responseTime = Date.now() - startTime;
      
      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        status = '系统中断';
      } else if (error.code === 'ECONNABORTED') {
        status = '系统中断'; // 超时
      } else {
        status = '系统中断';
      }

      console.log(`${website.name} 检查失败: ${error.message}`);
    }

    // 检查状态是否发生变化并记录历史
    const statusChanged = website.status !== status;
    const contentStatusChanged = website.contentStatus !== contentStatus;

    // 更新数据库
    try {
      await Website.findByIdAndUpdate(website._id, {
        previousStatus: website.status,
        status: status,
        previousContentStatus: website.contentStatus,
        responseTime: responseTime,
        lastChecked: new Date(),
        contentStatus: contentStatus,
        violationDetails: violationDetails,
        violationTypes: violationTypes
      });

      // 如果状态发生变化，记录到历史表
      if (statusChanged || contentStatusChanged) {
        await this.recordStatusChange(website, {
          previousStatus: website.status,
          currentStatus: status,
          previousContentStatus: website.contentStatus,
          currentContentStatus: contentStatus,
          statusChanged,
          contentStatusChanged,
          responseTime,
          violationDetails,
          violationTypes
        });
      }
    } catch (dbError) {
      console.error('更新数据库失败:', dbError);
    }

    return {
      website: website.name,
      websiteId: website._id,
      previousStatus: website.status,
      status: status,
      previousContentStatus: website.contentStatus,
      responseTime: responseTime,
      lastChecked: new Date(),
      contentStatus: contentStatus,
      violationDetails: violationDetails,
      violationTypes: violationTypes,
      statusChanged: statusChanged,
      contentStatusChanged: contentStatusChanged
    };
  }

  // 记录状态变化历史
  static async recordStatusChange(website, changeData) {
    try {
      let changeType = 'status_change';
      if (changeData.statusChanged && changeData.contentStatusChanged) {
        changeType = 'both';
      } else if (changeData.contentStatusChanged) {
        changeType = 'content_change';
      }

      const statusHistory = new StatusHistory({
        websiteId: website._id,
        websiteName: website.name,
        websiteUrl: website.url,
        previousStatus: changeData.previousStatus,
        currentStatus: changeData.currentStatus,
        previousContentStatus: changeData.previousContentStatus,
        currentContentStatus: changeData.currentContentStatus,
        changeType: changeType,
        responseTime: changeData.responseTime,
        violationDetails: changeData.violationDetails,
        violationTypes: changeData.violationTypes
      });

      await statusHistory.save();
      console.log(`记录状态变化: ${website.name} - ${changeData.previousStatus} -> ${changeData.currentStatus}`);

      return statusHistory;
    } catch (error) {
      console.error('记录状态变化失败:', error);
      return null;
    }
  }

  // 计算网站可用性
  static async calculateUptime(websiteId, days = 7) {
    try {
      // 这里可以扩展为记录历史监控数据
      // 目前简单返回当前状态
      const website = await Website.findById(websiteId);
      return website.status === '运行中' ? 100 : 0;
    } catch (error) {
      console.error('计算可用性失败:', error);
      return 0;
    }
  }

  // 检查网站内容（优化版：先检测内容变化，再进行AI检查）
  static async checkWebsiteContent(website, htmlContent) {
    try {
      // 使用cheerio解析HTML并提取文本内容
      const $ = cheerio.load(htmlContent);

      // 移除脚本和样式标签
      //$('script, style, noscript').remove();

      // 提取主要文本内容
      const textContent = $('body').text()
        .replace(/\s+/g, ' ')  // 替换多个空白字符为单个空格
        .trim();

      // 智能内容分段处理
      const contentSegments = this.segmentContent(textContent);

      // 计算内容哈希（基于完整内容）
      const currentContentHash = crypto.createHash('md5').update(textContent).digest('hex');

      // 检查内容是否发生变化
      const contentChanged = website.contentHash !== currentContentHash;

      console.log(`${website.name} 内容哈希检查:`);
      console.log(`  当前哈希: ${currentContentHash}`);
      console.log(`  之前哈希: ${website.contentHash || '无'}`);
      console.log(`  内容是否变化: ${contentChanged}`);

      // 如果内容没有变化，直接返回之前的检查结果
      if (!contentChanged && website.contentHash) {
        console.log(`${website.name} 内容未变化，跳过AI检查`);
        return {
          status: website.contentStatus || '正常',
          violationDetails: website.violationDetails,
          violationTypes: website.violationTypes,
          contentChanged: false,
          contentHash: currentContentHash
        };
      }

      // 内容发生变化，需要进行AI检查
      console.log(`${website.name} 内容发生变化，开始分段AI检查...`);
      const aiCheckResult = await this.checkContentWithAISegments(contentSegments, website.name);

      return {
        ...aiCheckResult,
        contentChanged: true,
        contentHash: currentContentHash
      };

    } catch (error) {
      console.error('内容检查失败:', error);
      return {
        status: '检查失败',
        violationDetails: error.message,
        violationTypes: null,
        contentChanged: false,
        contentHash: null
      };
    }
  }

  // 智能内容分段
  static segmentContent(textContent) {
    const maxSegmentLength = 1500; // 每段最大长度
    const maxSegments = 3; // 最多分段数
    const segments = [];

    // 如果内容较短，直接返回
    if (textContent.length <= maxSegmentLength) {
      return [textContent];
    }

    console.log(`内容长度: ${textContent.length}字符，开始分段处理...`);

    // 策略1: 按段落分割
    const paragraphs = textContent.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    if (paragraphs.length <= maxSegments && paragraphs.every(p => p.length <= maxSegmentLength)) {
      // 段落数量合适且每段长度合理，直接使用段落
      console.log(`使用段落分割策略，共${paragraphs.length}段`);
      return paragraphs.slice(0, maxSegments);
    }

    // 策略2: 智能分段 - 开头、中间、结尾
    const contentLength = textContent.length;
    console.log('使用三段式分割策略');

    // 开头部分（前1500字符）
    const startLength = Math.min(maxSegmentLength, Math.floor(contentLength / 3));
    segments.push(textContent.substring(0, startLength));

    // 中间部分（如果内容足够长）
    if (contentLength > maxSegmentLength * 2) {
      const middleStart = Math.floor(contentLength / 3);
      const middleEnd = Math.min(middleStart + maxSegmentLength, Math.floor(contentLength * 2 / 3));
      segments.push(textContent.substring(middleStart, middleEnd));
    }

    // 结尾部分（后1500字符）
    const endStart = Math.max(contentLength - maxSegmentLength, Math.floor(contentLength * 2 / 3));
    segments.push(textContent.substring(endStart));

    console.log(`分段完成，共${segments.length}段:`, segments.map(s => s.length + '字符'));
    return segments;
  }

  // 分段检查内容（对每个分段分别进行AI检查）
  static async checkContentWithAISegments(contentSegments, websiteName) {
    try {
      console.log(`${websiteName} 开始分段检查，共${contentSegments.length}段`);

      let hasViolation = false;
      let violationDetails = [];
      let violationTypes = [];

      // 对每个分段进行检查
      for (let i = 0; i < contentSegments.length; i++) {
        const segment = contentSegments[i];
        console.log(`${websiteName} 检查第${i + 1}段 (${segment.length}字符)...`);

        try {
          const segmentResult = await this.checkContentWithAI(segment);

          if (segmentResult.status === '违规') {
            hasViolation = true;
            violationDetails.push(`第${i + 1}段: ${segmentResult.violationDetails}`);
            if (segmentResult.violationTypes) {
              violationTypes.push(...segmentResult.violationTypes);
            }
            console.log(`${websiteName} 第${i + 1}段检测到违规: ${segmentResult.violationDetails}`);
          } else {
            console.log(`${websiteName} 第${i + 1}段检查正常`);
          }
        } catch (error) {
          console.error(`${websiteName} 第${i + 1}段检查失败:`, error.message);
          violationDetails.push(`第${i + 1}段检查失败: ${error.message}`);
        }

        // 添加延迟，避免过于频繁的API调用
        if (i < contentSegments.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 汇总结果
      const finalStatus = hasViolation ? '违规' : '正常';
      const finalViolationDetails = violationDetails.length > 0 ? violationDetails.join('\n') : null;
      const finalViolationTypes = violationTypes.length > 0 ? [...new Set(violationTypes)] : null;

      console.log(`${websiteName} 分段检查完成，最终结果: ${finalStatus}`);

      return {
        status: finalStatus,
        violationDetails: finalViolationDetails,
        violationTypes: finalViolationTypes
      };

    } catch (error) {
      console.error('分段内容检查失败:', error);
      return {
        status: '检查失败',
        violationDetails: error.message,
        violationTypes: ['系统错误']
      };
    }
  }

  // 使用Ollama本地AI检查内容
  static async checkContentWithAI(content) {
    try {
      const prompt = `请严格检查以下文本内容是否包含违规信息：

检查项目：
1. 政治敏感内容（反政府、分裂国家、颠覆政权等）
2. 色情低俗内容（explicit性描述、成人服务等）
3. 暴力恐怖内容（详细犯罪方法、恐怖主义宣传等）
4. 违法信息（毒品交易、赌博、诈骗等非法活动）
5. 其他违反中国法律法规的内容

请以JSON格式回答：
{
  "status": "违规" | "正常",
  "violationDetails": "违规详情或null",
  "violationTypes": ["违规类型"] | null
}

只返回JSON，不要其他说明。

内容：
${content}`;

      console.log('正在调用Ollama API进行内容检查...');

      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: "qwen3:32b",
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.1,
            top_p: 0.9,
            num_predict: 500
          }
        })
      };

      const response = await fetch('http://10.10.246.215:11434/api/generate', options);

      if (!response.ok) {
        throw new Error(`Ollama API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.response) {
        const aiResponse = result.response;

        try {
          console.log('Ollama AI原始响应:', aiResponse);

          // 清理响应内容，移除可能的markdown代码块标记
          const cleanResponse = aiResponse
            .replace(/```json\s*/g, '')
            .replace(/```\s*/g, '')
            .replace(/^[^{]*/, '') // 移除JSON前的文字
            .replace(/[^}]*$/, '') // 移除JSON后的文字
            .trim();

          // 尝试找到JSON部分
          let jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const parsedResult = JSON.parse(jsonMatch[0]);

            // 验证返回的JSON格式是否正确
            if (parsedResult.status && (parsedResult.status === '违规' || parsedResult.status === '正常')) {
              console.log('Ollama检查结果:', parsedResult.status);
              return {
                status: parsedResult.status,
                violationDetails: parsedResult.violationDetails || null,
                violationTypes: parsedResult.violationTypes || null
              };
            }
          }

          throw new Error('无法解析AI返回的JSON格式');

        } catch (parseError) {
          console.error('解析Ollama响应JSON失败:', parseError);
          console.error('原始响应:', aiResponse);

          // 如果JSON解析失败，使用关键词判断
          const violationKeywords = ['违规', '违法', '敏感', '不当', '禁止', '非法'];
          const hasViolation = violationKeywords.some(keyword => aiResponse.includes(keyword));

          if (hasViolation) {
            return {
              status: '违规',
              violationDetails: `AI检测到可能的违规内容，但JSON解析失败。原始响应：${aiResponse.substring(0, 200)}`,
              violationTypes: ['JSON解析失败-关键词检测']
            };
          } else {
            return {
              status: '正常',
              violationDetails: null,
              violationTypes: null
            };
          }
        }

      } else {
        throw new Error(`Ollama API响应格式错误: ${JSON.stringify(result)}`);
      }
      
    } catch (error) {
      console.error('AI内容检查失败:', error);
      return {
        status: '检查失败',
        violationDetails: error.message,
        violationTypes: ['系统错误']
      };
    }
  }

  // 检查并发送智能警报
  static async checkAndSendAlerts(checkResults) {
    try {
      console.log('开始智能警报检查...');

      // 获取需要接收警报的用户
      const User = require('../models/User');
      const users = await User.find({receiveAlerts: true}).sort({ createdAt: -1 });
      const teacherIds = users.map(user => user.employeeId);

      if (teacherIds.length === 0) {
        console.log('没有用户设置接收警报');
        return;
      }

      const userDetail = await userService.getUsersDetail({ corpUserIds: teacherIds });
      const messageService = new MessageService();

      // 分析每个网站的状态变化
      for (const item of checkResults) {
        if (!item.result) continue;

        const website = item.website;
        const result = item.result;

        // 检查网站状态变化
        if (result.statusChanged) {
          await this.handleStatusChange(website, result, messageService, userDetail);
        }

        // 检查内容状态变化
        if (result.contentStatusChanged) {
          await this.handleContentStatusChange(website, result, messageService, userDetail);
        }
      }

    } catch (error) {
      console.error('检查和发送警报失败:', error);
    }
  }

  // 处理网站状态变化
  static async handleStatusChange(website, result, messageService, userDetail) {
    try {
      const previousStatus = result.previousStatus;
      const currentStatus = result.status;

      console.log(`网站 ${website.name} 状态变化: ${previousStatus} -> ${currentStatus}`);

      // 从正常状态变为异常状态 - 发送警告
      if (previousStatus === '运行中' && currentStatus !== '运行中') {
        const statusText = currentStatus === '系统中断' ? '无法访问' :
                          currentStatus === '被修改' ? '页面被修改' : '维护中';

        const messageContent = `【网站状态警报】\n网站：${website.name}\n网址：${website.url}\n状态变化：运行中 → ${currentStatus}\n\n网站${statusText}，请及时处理！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        // 更新最后警报发送时间
        await Website.findByIdAndUpdate(website._id, {
          lastAlertSent: new Date()
        });

        console.log(`网站状态警报已发送: ${website.name} (${previousStatus} -> ${currentStatus})`);
      }

      // 从异常状态恢复为正常状态 - 发送恢复消息
      else if (previousStatus !== '运行中' && currentStatus === '运行中') {
        const messageContent = `【网站恢复通知】\n网站：${website.name}\n网址：${website.url}\n状态变化：${previousStatus} → 运行中\n\n网站已恢复正常运行！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        // 更新最后恢复警报发送时间
        await Website.findByIdAndUpdate(website._id, {
          lastRecoveryAlertSent: new Date()
        });

        console.log(`网站恢复通知已发送: ${website.name} (${previousStatus} -> ${currentStatus})`);
      }

    } catch (error) {
      console.error('处理网站状态变化失败:', error);
    }
  }

  // 处理内容状态变化
  static async handleContentStatusChange(website, result, messageService, userDetail) {
    try {
      const previousContentStatus = result.previousContentStatus;
      const currentContentStatus = result.contentStatus;

      console.log(`网站 ${website.name} 内容状态变化: ${previousContentStatus} -> ${currentContentStatus}`);

      // 从正常变为违规 - 发送违规警报
      if (previousContentStatus === '正常' && currentContentStatus === '违规') {
        const violationInfo = result.violationDetails || '检测到违规内容';
        const messageContent = `【网站内容违规警报】\n网站：${website.name}\n网址：${website.url}\n\n违规详情：\n${violationInfo}\n\n请立即处理！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        console.log(`网站内容违规警报已发送: ${website.name}`);
      }

      // 从违规恢复为正常 - 发送恢复消息
      else if (previousContentStatus === '违规' && currentContentStatus === '正常') {
        const messageContent = `【网站内容恢复通知】\n网站：${website.name}\n网址：${website.url}\n\n网站内容已恢复正常，违规问题已解决！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        console.log(`网站内容恢复通知已发送: ${website.name}`);
      }

    } catch (error) {
      console.error('处理内容状态变化失败:', error);
    }
  }

  // 判断是否需要发送警报
  static shouldSendAlert(currentStatus, previousStatus) {
    // 状态从正常变为异常时发送警报
    console.log("currentStatus",currentStatus)
    console.log("previousStatus",previousStatus)
    if (previousStatus === '运行中' && currentStatus !== '运行中') {
      return true;
    }
    
    // 持续异常状态（可以根据需要调整频率）
    if (currentStatus === '系统中断' || currentStatus === '被修改') {
      // 这里可以添加更复杂的逻辑，比如每隔一定时间重复发送
      return false; // 暂时只在状态变化时发送
    }
    
    return false;
  }
}

module.exports = MonitorService; 