const axios = require('axios');
const Website = require('../models/Website');
const StatusHistory = require('../models/StatusHistory');
const MessageService = require('../util/message');
const userService = require('../util/user');
const cheerio = require('cheerio');

// 如果Node.js版本小于18，需要安装node-fetch
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  fetch = require('node-fetch');
}

class MonitorService {
  // 检查所有网站
  static async checkAllWebsites() {
    try {
      const websites = await Website.find({  });
      console.log(`开始检查 ${websites.length} 个网站...`);
      
      const checkPromises = websites.map(website => this.checkSingleWebsite(website));
      const results = await Promise.allSettled(checkPromises);
      
      // 收集检查结果
      const checkResults = results.map((result, index) => ({
        website: websites[index],
        result: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }));

      // 检查是否需要发送警报
      await this.checkAndSendAlerts(checkResults);
      
      console.log('所有网站检查完成');
    } catch (error) {
      console.error('批量检查网站时出错:', error);
    }
  }

  // 检查单个网站
  static async checkSingleWebsite(website) {
    const startTime = Date.now();
    let status = '系统中断';
    let responseTime = 0;
    let contentStatus = '正常';
    let violationDetails = null;
    let violationTypes = null;

    try {
      console.log(`检查网站: ${website.name} - ${website.url}`);
      
      const response = await axios.get(website.url, {
        timeout: 10000, // 10秒超时
        validateStatus: function (status) {
          return status < 500; // 状态码小于500都认为是成功
        }
      });

      responseTime = Date.now() - startTime;

      if (response.status >= 200 && response.status < 400) {
        status = '运行中';
        
        // 进行内容检查
        try {
          const contentCheckResult = await this.checkWebsiteContent(response.data);
          contentStatus = contentCheckResult.status;
          violationDetails = contentCheckResult.violationDetails;
          violationTypes = contentCheckResult.violationTypes;
          
          console.log(`${website.name} 内容检查结果: ${contentStatus}`);
          if (violationDetails) {
            console.log(`违规详情: ${violationDetails}`);
            console.log(`违规类型: ${JSON.stringify(violationTypes)}`);
          }
        } catch (contentError) {
          console.error(`${website.name} 内容检查失败:`, contentError);
          contentStatus = '检查失败';
        }
        
      } else if (response.status >= 400 && response.status < 500) {
        status = '被修改'; // 客户端错误，可能是页面被修改
      } else {
        status = '系统中断';
      }

      console.log(`${website.name} 检查结果: ${status}, 响应时间: ${responseTime}ms`);

    } catch (error) {
      responseTime = Date.now() - startTime;
      
      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        status = '系统中断';
      } else if (error.code === 'ECONNABORTED') {
        status = '系统中断'; // 超时
      } else {
        status = '系统中断';
      }

      console.log(`${website.name} 检查失败: ${error.message}`);
    }

    // 检查状态是否发生变化并记录历史
    const statusChanged = website.status !== status;
    const contentStatusChanged = website.contentStatus !== contentStatus;

    // 更新数据库
    try {
      await Website.findByIdAndUpdate(website._id, {
        previousStatus: website.status,
        status: status,
        previousContentStatus: website.contentStatus,
        responseTime: responseTime,
        lastChecked: new Date(),
        contentStatus: contentStatus,
        violationDetails: violationDetails,
        violationTypes: violationTypes
      });

      // 如果状态发生变化，记录到历史表
      if (statusChanged || contentStatusChanged) {
        await this.recordStatusChange(website, {
          previousStatus: website.status,
          currentStatus: status,
          previousContentStatus: website.contentStatus,
          currentContentStatus: contentStatus,
          statusChanged,
          contentStatusChanged,
          responseTime,
          violationDetails,
          violationTypes
        });
      }
    } catch (dbError) {
      console.error('更新数据库失败:', dbError);
    }

    return {
      website: website.name,
      websiteId: website._id,
      previousStatus: website.status,
      status: status,
      previousContentStatus: website.contentStatus,
      responseTime: responseTime,
      lastChecked: new Date(),
      contentStatus: contentStatus,
      violationDetails: violationDetails,
      violationTypes: violationTypes,
      statusChanged: statusChanged,
      contentStatusChanged: contentStatusChanged
    };
  }

  // 记录状态变化历史
  static async recordStatusChange(website, changeData) {
    try {
      let changeType = 'status_change';
      if (changeData.statusChanged && changeData.contentStatusChanged) {
        changeType = 'both';
      } else if (changeData.contentStatusChanged) {
        changeType = 'content_change';
      }

      const statusHistory = new StatusHistory({
        websiteId: website._id,
        websiteName: website.name,
        websiteUrl: website.url,
        previousStatus: changeData.previousStatus,
        currentStatus: changeData.currentStatus,
        previousContentStatus: changeData.previousContentStatus,
        currentContentStatus: changeData.currentContentStatus,
        changeType: changeType,
        responseTime: changeData.responseTime,
        violationDetails: changeData.violationDetails,
        violationTypes: changeData.violationTypes
      });

      await statusHistory.save();
      console.log(`记录状态变化: ${website.name} - ${changeData.previousStatus} -> ${changeData.currentStatus}`);

      return statusHistory;
    } catch (error) {
      console.error('记录状态变化失败:', error);
      return null;
    }
  }

  // 计算网站可用性
  static async calculateUptime(websiteId, days = 7) {
    try {
      // 这里可以扩展为记录历史监控数据
      // 目前简单返回当前状态
      const website = await Website.findById(websiteId);
      return website.status === '运行中' ? 100 : 0;
    } catch (error) {
      console.error('计算可用性失败:', error);
      return 0;
    }
  }

  // 检查网站内容
  static async checkWebsiteContent(htmlContent) {
    try {
      // 使用cheerio解析HTML并提取文本内容
      const $ = cheerio.load(htmlContent);
      
      // 移除脚本和样式标签
      $('script, style, noscript').remove();
      
      // 提取主要文本内容
      const textContent = $('body').text()
        .replace(/\s+/g, ' ')  // 替换多个空白字符为单个空格
        .trim();
      
      // 如果文本内容太短，认为是正常的
      if (textContent.length < 100) {
        return {
          status: '正常',
          violationDetails: null,
          violationTypes: null
        };
      }
      
      // 限制文本长度，避免过长的内容
      const contentToCheck = textContent.substring(0, 2000);
      
      // 调用大模型API检查内容
      const aiCheckResult = await this.checkContentWithAI(contentToCheck);
      
      return aiCheckResult;
    } catch (error) {
      console.error('内容检查失败:', error);
      return {
        status: '检查失败',
        violationDetails: error.message,
        violationTypes: null
      };
    }
  }

  // 使用AI检查内容
  static async checkContentWithAI(content) {
    try {
      const prompt = `请严格检查以下文本内容是否包含以下违规信息，并逐条说明判断依据：

政治敏感内容（如反政府、分裂国家、颠覆政权等）
色情低俗内容（如explicit性描述、成人服务等）
暴力恐怖内容（如详细犯罪方法、恐怖主义宣传等）
违法信息（如毒品交易、赌博、诈骗等非法活动）
其他违反中国法律法规或社会主义核心价值观的内容

请务必以JSON格式回答，格式如下：
{
  "status": "违规" | "正常",
  "violationDetails": "如果违规，请详细说明违规原因和类型；如果正常，此字段为null",
  "violationTypes": ["违规类型1", "违规类型2"] | null
}

注意：请只返回JSON对象，不要包含其他文字说明。

待检查内容：
${content}`;

      const options = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer sk-yjarxfwyvsmtexalcqfyoirdsxndbbkndvebcxhzinhrugfu`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: "deepseek-ai/DeepSeek-R1",
          messages: [
            {
              role: "user",
              content: prompt
            }
          ]
        })
      };

      const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', options);
      const result = await response.json();
      
      if (result.choices && result.choices[0] && result.choices[0].message) {
        const aiResponse = result.choices[0].message.content;
        
        try {
          // 尝试解析AI返回的JSON
          console.log('AI原始响应:', aiResponse);
          
          // 清理响应内容，移除可能的markdown代码块标记
          const cleanResponse = aiResponse
            .replace(/```json\s*/g, '')
            .replace(/```\s*/g, '')
            .trim();
          
          const parsedResult = JSON.parse(cleanResponse);
          
          // 验证返回的JSON格式是否正确
          if (parsedResult.status && (parsedResult.status === '违规' || parsedResult.status === '正常')) {
            return {
              status: parsedResult.status,
              violationDetails: parsedResult.violationDetails || null,
              violationTypes: parsedResult.violationTypes || null
            };
          } else {
            throw new Error('AI返回的JSON格式不正确');
          }
          
        } catch (parseError) {
          console.error('解析AI响应JSON失败:', parseError);
          console.error('AI原始响应:', aiResponse);
          
          // 如果JSON解析失败，回退到原来的简单判断
          if (aiResponse.includes('违规')) {
            return {
              status: '违规',
              violationDetails: aiResponse,
              violationTypes: ['解析失败-原始响应包含违规关键词']
            };
          } else {
            return {
              status: '正常',
              violationDetails: null,
              violationTypes: null
            };
          }
        }
        
      } else {
        throw new Error('AI API响应格式错误');
      }
      
    } catch (error) {
      console.error('AI内容检查失败:', error);
      return {
        status: '检查失败',
        violationDetails: error.message,
        violationTypes: ['系统错误']
      };
    }
  }

  // 检查并发送智能警报
  static async checkAndSendAlerts(checkResults) {
    try {
      console.log('开始智能警报检查...');

      // 获取需要接收警报的用户
      const User = require('../models/User');
      const users = await User.find({receiveAlerts: true}).sort({ createdAt: -1 });
      const teacherIds = users.map(user => user.employeeId);

      if (teacherIds.length === 0) {
        console.log('没有用户设置接收警报');
        return;
      }

      const userDetail = await userService.getUsersDetail({ corpUserIds: teacherIds });
      const messageService = new MessageService();

      // 分析每个网站的状态变化
      for (const item of checkResults) {
        if (!item.result) continue;

        const website = item.website;
        const result = item.result;

        // 检查网站状态变化
        if (result.statusChanged) {
          await this.handleStatusChange(website, result, messageService, userDetail);
        }

        // 检查内容状态变化
        if (result.contentStatusChanged) {
          await this.handleContentStatusChange(website, result, messageService, userDetail);
        }
      }

    } catch (error) {
      console.error('检查和发送警报失败:', error);
    }
  }

  // 处理网站状态变化
  static async handleStatusChange(website, result, messageService, userDetail) {
    try {
      const previousStatus = result.previousStatus;
      const currentStatus = result.status;

      console.log(`网站 ${website.name} 状态变化: ${previousStatus} -> ${currentStatus}`);

      // 从正常状态变为异常状态 - 发送警告
      if (previousStatus === '运行中' && currentStatus !== '运行中') {
        const statusText = currentStatus === '系统中断' ? '无法访问' :
                          currentStatus === '被修改' ? '页面被修改' : '维护中';

        const messageContent = `【网站状态警报】\n网站：${website.name}\n网址：${website.url}\n状态变化：运行中 → ${currentStatus}\n\n网站${statusText}，请及时处理！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        // 更新最后警报发送时间
        await Website.findByIdAndUpdate(website._id, {
          lastAlertSent: new Date()
        });

        console.log(`网站状态警报已发送: ${website.name} (${previousStatus} -> ${currentStatus})`);
      }

      // 从异常状态恢复为正常状态 - 发送恢复消息
      else if (previousStatus !== '运行中' && currentStatus === '运行中') {
        const messageContent = `【网站恢复通知】\n网站：${website.name}\n网址：${website.url}\n状态变化：${previousStatus} → 运行中\n\n网站已恢复正常运行！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        // 更新最后恢复警报发送时间
        await Website.findByIdAndUpdate(website._id, {
          lastRecoveryAlertSent: new Date()
        });

        console.log(`网站恢复通知已发送: ${website.name} (${previousStatus} -> ${currentStatus})`);
      }

    } catch (error) {
      console.error('处理网站状态变化失败:', error);
    }
  }

  // 处理内容状态变化
  static async handleContentStatusChange(website, result, messageService, userDetail) {
    try {
      const previousContentStatus = result.previousContentStatus;
      const currentContentStatus = result.contentStatus;

      console.log(`网站 ${website.name} 内容状态变化: ${previousContentStatus} -> ${currentContentStatus}`);

      // 从正常变为违规 - 发送违规警报
      if (previousContentStatus === '正常' && currentContentStatus === '违规') {
        const violationInfo = result.violationDetails || '检测到违规内容';
        const messageContent = `【网站内容违规警报】\n网站：${website.name}\n网址：${website.url}\n\n违规详情：\n${violationInfo}\n\n请立即处理！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        console.log(`网站内容违规警报已发送: ${website.name}`);
      }

      // 从违规恢复为正常 - 发送恢复消息
      else if (previousContentStatus === '违规' && currentContentStatus === '正常') {
        const messageContent = `【网站内容恢复通知】\n网站：${website.name}\n网址：${website.url}\n\n网站内容已恢复正常，违规问题已解决！`;

        await messageService.sendText({
          content: messageContent,
          toUserList: userDetail.map(user => user.userId)
        });

        console.log(`网站内容恢复通知已发送: ${website.name}`);
      }

    } catch (error) {
      console.error('处理内容状态变化失败:', error);
    }
  }

  // 判断是否需要发送警报
  static shouldSendAlert(currentStatus, previousStatus) {
    // 状态从正常变为异常时发送警报
    console.log("currentStatus",currentStatus)
    console.log("previousStatus",previousStatus)
    if (previousStatus === '运行中' && currentStatus !== '运行中') {
      return true;
    }
    
    // 持续异常状态（可以根据需要调整频率）
    if (currentStatus === '系统中断' || currentStatus === '被修改') {
      // 这里可以添加更复杂的逻辑，比如每隔一定时间重复发送
      return false; // 暂时只在状态变化时发送
    }
    
    return false;
  }
}

module.exports = MonitorService; 