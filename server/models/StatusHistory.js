const mongoose = require('mongoose');

const statusHistorySchema = new mongoose.Schema({
  websiteId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Website',
    required: true
  },
  websiteName: {
    type: String,
    required: true
  },
  websiteUrl: {
    type: String,
    required: true
  },
  previousStatus: {
    type: String,
    enum: ['运行中', '系统中断', '被修改', '维护中'],
    default: null
  },
  currentStatus: {
    type: String,
    enum: ['运行中', '系统中断', '被修改', '维护中'],
    required: true
  },
  previousContentStatus: {
    type: String,
    enum: ['正常', '违规', '检查失败'],
    default: null
  },
  currentContentStatus: {
    type: String,
    enum: ['正常', '违规', '检查失败'],
    required: true
  },
  changeType: {
    type: String,
    enum: ['status_change', 'content_change', 'both'],
    required: true
  },
  alertSent: {
    type: Boolean,
    default: false
  },
  alertType: {
    type: String,
    enum: ['warning', 'recovery', 'violation'],
    required: false
  },
  responseTime: {
    type: Number,
    default: 0
  },
  violationDetails: {
    type: String,
    default: null
  },
  violationTypes: {
    type: [String],
    default: null
  }
}, {
  timestamps: true
});

// 索引
statusHistorySchema.index({ websiteId: 1, createdAt: -1 });
statusHistorySchema.index({ changeType: 1, createdAt: -1 });
statusHistorySchema.index({ alertSent: 1, alertType: 1 });

module.exports = mongoose.model('StatusHistory', statusHistorySchema);
