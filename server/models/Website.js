const mongoose = require('mongoose');

const websiteSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true
  },
  url: {
    type: String,
    required: true
  },
  networkType: {
    type: String,
    enum: ['内网', '外网'],
    default: '内网'
  },
  status: {
    type: String,
    enum: ['运行中', '系统中断', '被修改', '维护中'],
    default: '运行中'
  },
  previousStatus: {
    type: String,
    enum: ['运行中', '系统中断', '被修改', '维护中'],
    default: null
  },
  operatingHours: {
    start: {
      type: String,
      default: '00:00'
    },
    end: {
      type: String,
      default: '23:59'
    }
  },
  lastChecked: {
    type: Date,
    default: Date.now
  },
  responseTime: {
    type: Number,
    default: 0
  },
  uptime: {
    type: Number,
    default: 100
  },
  isActive: {
    type: Boolean,
    default: true
  },
  contentStatus: {
    type: String,
    enum: ['正常', '违规', '检查失败'],
    default: '正常'
  },
  previousContentStatus: {
    type: String,
    enum: ['正常', '违规', '检查失败'],
    default: null
  },
  violationDetails: {
    type: String,
    default: null
  },
  violationTypes: {
    type: [String],
    default: null
  },
  contentHash: {
    type: String,
    default: null
  },
  lastContentCheck: {
    type: Date,
    default: null
  },
  lastAlertSent: {
    type: Date,
    default: null
  },
  lastRecoveryAlertSent: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Website', websiteSchema);