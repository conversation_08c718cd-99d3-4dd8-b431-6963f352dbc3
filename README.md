# 网站监控系统

一个基于 Node.js + MongoDB + Vue3 + Vant 的移动端网站监控系统。

## 功能特性

- 📱 **移动端优先设计** - 使用 Vant UI 组件库，完美适配移动设备
- 🔍 **实时监控** - 定时检查网站状态，自动更新监控数据
- 📊 **数据统计** - 实时展示网站运行状态统计信息
- 🏷️ **分类管理** - 支持内网/外网分类，不同状态标签显示
- ⏰ **时间管理** - 支持设置网站运行时间段
- 🔄 **手动检查** - 支持手动触发单个网站状态检查
- 💾 **数据持久化** - 使用 MongoDB 存储监控数据

## 技术栈

### 后端
- **Node.js** - 服务器运行环境
- **Express** - Web 应用框架
- **MongoDB** - 数据库
- **Mongoose** - MongoDB 对象建模工具
- **node-cron** - 定时任务调度
- **axios** - HTTP 客户端

### 前端
- **Vue 3** - 前端框架
- **Vant 4** - 移动端 UI 组件库
- **Vue Router** - 路由管理
- **Vuex** - 状态管理
- **axios** - HTTP 客户端

## 快速开始

### 环境要求

- Node.js >= 14.0.0
- MongoDB >= 4.0
- npm 或 yarn

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <your-repo-url>
   cd website-monitor
   ```

2. **安装后端依赖**
   ```bash
   npm install
   ```

3. **安装前端依赖**
   ```bash
   npm run install-client
   ```

4. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，设置你的 MongoDB 连接字符串
   ```

5. **启动 MongoDB**
   ```bash
   # macOS (使用 Homebrew)
   brew services start mongodb-community
   
   # Ubuntu/Linux
   sudo systemctl start mongod
   
   # Windows
   net start MongoDB
   ```

6. **初始化示例数据**
   ```bash
   node server/scripts/initData.js
   ```

7. **启动后端服务**
   ```bash
   npm run dev
   ```

8. **启动前端开发服务器**
   ```bash
   npm run client
   ```

9. **访问应用**
   - 前端: http://localhost:8080
   - 后端 API: http://localhost:3000

## 项目结构

```
website-monitor/
├── server/                 # 后端代码
│   ├── app.js             # 主应用文件
│   ├── models/            # 数据模型
│   ├── routes/            # 路由
│   ├── services/          # 业务逻辑
│   └── scripts/           # 脚本文件
├── client/                # 前端代码
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── views/         # 页面视图
│   │   ├── router/        # 路由配置
│   │   ├── store/         # 状态管理
│   │   └── services/      # API 服务
│   ├── public/
│   └── vue.config.js      # Vue 配置
├── package.json           # 项目配置
├── .env                   # 环境变量
└── README.md              # 项目说明
```

## API 接口

### 网站监控

- `GET /api/monitor/websites` - 获取所有网站
- `POST /api/monitor/websites` - 添加新网站
- `PUT /api/monitor/websites/:id` - 更新网站信息
- `DELETE /api/monitor/websites/:id` - 删除网站
- `POST /api/monitor/check/:id` - 手动检查网站
- `GET /api/monitor/stats` - 获取统计信息

## 部署

### 生产环境部署

1. **构建前端**
   ```bash
   npm run build
   ```

2. **配置生产环境变量**
   ```bash
   export NODE_ENV=production
   export MONGODB_URI=mongodb://your-production-db-url
   export PORT=3000
   ```

3. **启动应用**
   ```bash
   npm start
   ```

### Docker 部署 (可选)

```bash
# 构建镜像
docker build -t website-monitor .

# 运行容器
docker run -p 3000:3000 -e MONGODB_URI=mongodb://host.docker.internal:27017/website_monitor website-monitor
```

## 配置说明

### 环境变量

- `NODE_ENV` - 运行环境 (development/production)
- `PORT` - 服务器端口 (默认: 3000)
- `MONGODB_URI` - MongoDB 连接字符串

### 监控配置

- 默认每分钟执行一次网站状态检查
- 网站请求超时时间: 10秒
- 可以通过修改 `server/app.js` 中的 cron 表达式来调整检查频率

## 功能截图

### 主界面
- 监控管理页面，显示所有网站状态
- 统计信息卡片，显示总体运行情况
- 支持下拉刷新和滑动操作

### 添加网站
- 表单验证，确保数据完整性
- 时间选择器，设置运行时间段
- 网络类型选择 (内网/外网)

### 网站详情
- 详细的网站信息展示
- 实时状态检查功能
- 编辑和删除操作

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License 