{"name": "website-monitor", "version": "1.0.0", "description": "网站监控系统", "main": "server/app.js", "scripts": {"start": "node server/app.js", "dev": "nodemon server/app.js", "client": "cd client && npm run serve", "build": "cd client && npm run build", "install-client": "cd client && npm install"}, "dependencies": {"axios": "^1.5.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mongoose": "^7.5.0", "node-cron": "^3.0.2", "node-fetch": "^2.7.0", "redis": "^5.5.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["monitor", "nodejs", "mongodb", "vue"], "author": "", "license": "MIT"}