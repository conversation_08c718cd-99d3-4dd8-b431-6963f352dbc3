#!/bin/bash

echo "🚀 启动网站监控系统..."

# 检查是否安装了MongoDB
if ! command -v mongod &> /dev/null; then
    echo "❌ MongoDB 未安装，请先安装 MongoDB"
    echo "💡 macOS: brew install mongodb-community"
    echo "💡 Ubuntu: sudo apt install mongodb"
    exit 1
fi

# 检查MongoDB是否运行
if ! pgrep -f mongod > /dev/null; then
    echo "📦 启动 MongoDB..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew services start mongodb-community
    else
        sudo systemctl start mongod
    fi
    sleep 3
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📥 安装后端依赖..."
    npm install
fi

if [ ! -d "client/node_modules" ]; then
    echo "📥 安装前端依赖..."
    cd client && npm install && cd ..
fi

# 初始化数据（如果需要）
if [ "$1" = "--init-data" ]; then
    echo "🔄 初始化示例数据..."
    node server/scripts/initData.js
fi

echo "✅ 启动后端服务 (端口: 3000)..."
npm run dev &
BACKEND_PID=$!

echo "✅ 启动前端服务 (端口: 8080)..."
npm run client &
FRONTEND_PID=$!

echo ""
echo "🎉 网站监控系统启动成功！"
echo "📱 前端地址: http://localhost:8080"
echo "🔧 后端 API: http://localhost:3000"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap 'echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait 